<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd">
    
    <!-- 配置包扫描器    -->
    <context:component-scan base-package="com.gl.controller"></context:component-scan>
    
    <!-- 配置注解驱动 -->
    <mvc:annotation-driven/>
	
	<!-- 静态资源映射 -->
	
	<mvc:resources location="/GetTXT/" mapping="/GetTXT/**"/>
	<mvc:resources location="/static/" mapping="/static/**"/>
	<mvc:resources location="/assets/" mapping="/assets/**"/>
	
	<!-- 多媒体解析器 -->
	<bean id="multipartResolver"
		class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<!-- 设定默认编码 -->
		<property name="defaultEncoding" value="UTF-8"></property>
		<!-- 设定文件上传的最大值5MB，5*1024*1024 -->
		<property name="maxUploadSize" value="5242880"></property>
	</bean>
	
	<!--token校验-->
	<mvc:interceptors>
    	<mvc:interceptor>
            <mvc:mapping path="/api/**"/>
            <bean class="com.gl.handler.Authentication"/> 
        </mvc:interceptor>
    </mvc:interceptors>
</beans>        