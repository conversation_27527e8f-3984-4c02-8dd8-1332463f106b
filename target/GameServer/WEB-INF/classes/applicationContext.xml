<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
	   xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
	   xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.2.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
		http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa-1.3.xsd">

	<!-- 加载配置属性文件 -->
	<context:property-placeholder
			ignore-unresolvable="true" location="classpath:important.properties" />

	<!-- SqlSessionFactory -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<!--指定连接资源 -->
		<property name="dataSource" ref="dataSource" />
		<!--指定映射文件 -->
		<property name="mapperLocations" value="classpath:mappings/*.xml" />
		<!-- 当mybatis插入空值时自动为null -->
		<property name="configLocation" value="classpath:SqlMapConfig.xml"/>
	</bean>

	<!-- 批量配置mapper代理类 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="org.come.mapper" />
		<property name="annotationClass" value="org.come.annotation.MyBatisAnnotation" />
	</bean>
	<!-- 数据源配置, 使用 BoneCP 数据库连接池 -->
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  init-method="init" destroy-method="close">
		<!-- 数据源驱动类可不写，Druid默认会自动根据URL识别DriverClass -->
		<property name="driverClassName" value="${jdbc.driver}" />

		<!-- 基本属性 url、user、password -->
		<property name="url" value="${jdbc.url}" />
		<property name="username" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />

		<!-- 配置初始化大小、最小、最大 -->
		<property name="initialSize" value="${jdbc.pool.init}" />
		<property name="minIdle" value="${jdbc.pool.minIdle}" />
		<property name="maxActive" value="${jdbc.pool.maxActive}" />

		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="60000" />

		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000" />

		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000" />

		<property name="validationQuery" value="${jdbc.testSql}" />
		<property name="testWhileIdle" value="true" />
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<!--removeAbandoned: 是否自动回收超时连接-->
		<property name="removeAbandoned"  value="true"/>
		<!--removeAbandonedTimeout: 超时时间(以秒数为单位)-->
		<property name="removeAbandonedTimeout" value="10"/>

		<!-- 打开PSCache，并且指定每个连接上PSCache的大小（Oracle使用） -->
		<!--  		<property name="poolPreparedStatements" value="true" />  -->
		<!--  		<property name="maxPoolPreparedStatementPerConnectionSize" value="20" />  -->


		<!-- 配置监控统计拦截的filters -->
		<property name="filters" value="stat" />
	</bean>
</beans>