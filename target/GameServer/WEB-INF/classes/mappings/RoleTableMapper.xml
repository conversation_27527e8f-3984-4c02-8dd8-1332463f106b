<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.come.mapper.RoleTableMapper">
	<sql id="LOGINRESULT_LIST">
		BON<PERSON>,
		SPIR,
		POWER,
		SPEED,
		GANGNAME,
		PASSWORD,
		SAVEGOLD,
		GANGPOST,
		CONTRIBUTION,
		ACHIEVEMENT,
		NEWROLE,
		RESISTANCE,
		BORN,
		BORN1,
		TASKDAILY,
		UPTIME,
		TIMINGGOOD,
		TURNAROUND,
		MARRYOBJECT,
		<PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON>ALNA<PERSON>,
		SEX,
		RACE_NAME,
		USER_ID,
		X,
		Y,
		MAPID,
		USERNAME,
		USERPWD,
		SPECIES_ID,
		GANG_ID,
		<PERSON><PERSON><PERSON>H_ID,
		<PERSON><PERSON><PERSON>RD,
		EXPERIENCE,
		GOLD,
		<PERSON><PERSON><PERSON>,
		HP,
		<PERSON><PERSON><PERSON>T_<PERSON>,
		MP,
		P<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON>_<PERSON>,
		<PERSON><PERSON><PERSON>_<PERSON>,
		<PERSON><PERSON><PERSON><PERSON><PERSON>,
		<PERSON>UMMO<PERSON>NG_ID,
		<PERSON><PERSON><PERSON>,
		T<PERSON><PERSON>_ID,
		<PERSON><PERSON>,
		SER<PERSON>RMESTRING,
        taskComplete,
        taskData,
        FIGHTING,
        DBEXP,
        SCORE,
        KILL,
        babyId,
        MONEY,
        DRAWING,
        SKILL_ID,
        CALM,
        FMSLD,
	ZHONGTIAN,
	QINGLONG,
	BAIHU,
	XUANWU,
	ZHUQUE,
	SHOUHU,
	JIESUO,
	SH,

        XIUWEI,
        EXTRAPOINT,
		EQUIPMENTS,
        Paysum,Daypaysum,Dayandpayorno,Dayfirstinorno,Daygetorno,Vipget,lowOrHihtpack,PALS,meridians,ATTACHPACK,hjmax,xingpans,online_time as onlineTime,qian_dao as qianDao,gmshoptype,TTVICTORY,TTFAIL,TTRECORD,TTJIANGLI,GRADEINCREASE,gameTimeRemaining,CURRENTATTRIBUTE,EXT_POINT extPoint,LIANGHAO as liangHao,LIANGHAOTYPE as lianghaotype,LIANGHAOVALUE as lianghaoValue,LIANGHAOEXPIRE as lianghaoexpire,CONTINUEPRICE as continueprice,TRANSFERGOLD,MONEYSHOP,GROWTHFUND
	</sql>

	<sql id="ROLETABLE_LIST">
		ROLE_ID,
		USER_ID,
		SPECIES_ID,
		SUMMONING_ID,
		GANG_ID,
		MOUNT_ID,
		TROOP_ID,
		RACE_ID,
		SKILL_ID,
		BOOTH_ID,
		TASK_ID,
		HP,
		MP,
		GOLD,
		CODECARD,
		EXPERIENCE,
		GRADE,
		PRESTIGE,
		PKRECORD,
		ROLENAME,
		TITLE,
		PATH,
		SEX,
		LOCALNAME,
		SKILL,
		X,
		Y,
		MAPID,
		UPTIME,
		GANGPOST,
		ACHIEVEMENT,
		CONTRIBUTION,
		BONE,
		SPIR,
		POWER,
		SPEED,
		CAPTAIN,
		SAVEGOLD,
		PASSWORD,
		GANGNAME,
		HAVEBABY,
		NEWROLE,
		RACENAME,
		MAXEXP,
		MARRYOBJECT,
		SKILLS,
		TIMINGGOOD,
		TURNAROUND,
		TASKDAILY,
		BORN,
		RESISTANCE,
		SERVERMESTRING,FIGHTING,DBEXP,SCORE,KILL,BABYID,DRAWING,SKILL_ID,
		CALM,
        FMSLD,
        XIUWEI,
        EXTRAPOINT, ZHONGTIAN,QINGLONG,BAIHU,XUANWU,ZHUQUE,SHOUHU,JIESUO,SH,
		EQUIPMENTS,
		BORN1,
        Paysum,Daypaysum,Dayandpayorno,Dayfirstinorno,Daygetorno,Vipget,lowOrHihtpack,meridians,ATTACHPACK,hjmax,qian_dao as qianDao,online_time as onlineTime,gmshoptype,GRADEINCREASE,gameTimeRemaining,CURRENTATTRIBUTE,EXT_POINT,TRANSFERGOLD,MONEYSHOP,GROWTHFUND
	</sql>
	<sql id="GANG_LIST">
	    ROLE_ID,ROLENAME,GANGPOST,RACE_NAME,GRADE,CONTRIBUTION,UPTIME
	</sql>
	<!-- 查找角色名是否存在 -->
	<select id="selectRoleTableByRoleName" parameterType="String"
			resultType="org.come.entity.RoleTable">
		SELECT
		<include refid="ROLETABLE_LIST" />
		FROM role_table WHERE rolename = #{ rolename }
	</select>
	<!-- 创建角色 -->
	<insert id="insertIntoRoleTable" parameterType="org.come.bean.LoginResult">
		<!-- <selectKey keyProperty="role_id" resultType="java.math.BigDecimal"
			order="BEFORE">
			select seq_roletable_id.nextval from dual
		</selectKey> -->
		insert into
		role_table(role_id,user_id,species_id,race_id,hp,mp,
		rolename,sex,localname,title,SERVERMESTRING,ext_point)
		values(#{role_id},#{user_id},#{species_id},#{race_id},#{hp},#{mp},
		#{rolename},#{sex},#{localname},#{title},#{serverMeString},#{extPoint})
	</insert>

	<!-- 根据帮派ID查找帮派 -->
	<select id="findGangManberByGangID" parameterType="java.math.BigDecimal"
			resultType="org.come.bean.LoginResult">
		SELECT
		<include refid="GANG_LIST" />
		FROM LOGINTABLE WHERE GANG_ID = #{ gang_id }
	</select>
	<!-- 根据角色id查询金钱 -->
	<select id="selectMoneyRoleID" parameterType="java.math.BigDecimal"
			resultType="java.math.BigDecimal">
		SELECT GOLD
		FROM LOGINTABLE WHERE ROLE_ID = #{ role_id }
	</select>
	<!-- 根据角色id修改金钱 -->
	<update id="updateMoneyRoleID">
		UPDATE role_table
		SET gold=nvl(#{ gold },gold)
		WHERE role_id = #{ role_id }
	</update>
	<!-- 根据角色id修改积分 -->
	<update id="updateMoneyUserID">
		UPDATE USERTABLE
		SET MONEY=nvl(#{ MONEY },MONEY)
		WHERE USER_ID = #{ USER_ID }
	</update>
	<update id="updateSavaMoneyRoleID">
		UPDATE role_table
		SET savegold=nvl(#{ gold },savegold)
		WHERE role_id = #{ role_id }
	</update>
	<select id="selectForUserId"  resultType="org.come.entity.UserTable">

		select * from usertable WHERE USER_ID=#{USER_ID}
	</select>
	<!-- 修改角色帮派信息 -->
	<update id="updateRole" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET
		GANG_ID = nvl(#{ gang_id },0),
		gangpost=#{ gangpost },
		gangname=#{ gangname }
		WHERE role_id = #{ role_id }
	</update>
	<!-- 修改角色名称伴侣信息 -->
	<update id="updateByPrimaryKey" parameterType="org.come.entity.RoleTable">
    update ROLE_TABLE set
      ROLENAME =#{rolename,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      MARRYOBJECT = #{marryObject,jdbcType=VARCHAR}
    where ROLE_ID = #{role_id,jdbcType=DECIMAL}
  </update>
	<!-- 退出时修改角色信息 -->
	<update id="updateRoleWhenExit" parameterType="org.come.bean.LoginResult">
		UPDATE ROLE_TABLE SET
		species_id=nvl(#{ species_id },species_id),
		summoning_id=#{
		summoning_id },
		mount_id=#{ mount_id },
		race_id=nvl(#{ race_id },race_id),
		hp=nvl(#{ hp },hp),
		mp=nvl(#{ mp },mp),
		gold=nvl(#{ gold },gold),
		codecard=nvl(#{ codecard },codecard),
		experience=nvl(#{ experience },experience),
		grade=nvl(#{ grade },grade),
		prestige=nvl(#{ prestige },prestige),
		pkrecord=nvl(#{ pkrecord },pkrecord),
		rolename=nvl(#{ rolename },rolename),
		title=nvl(#{ title },title),
		sex=nvl(#{ sex },sex),
		localname=nvl(#{ localname },localname),
		achievement=nvl(#{ achievement },achievement),
		contribution=nvl(#{ contribution },contribution),
		bone=nvl(#{ bone },bone),
		spir=nvl(#{ spir },spir),
		power=nvl(#{ power },power),
		speed=nvl(#{ speed },speed),
		savegold=nvl(#{ savegold },savegold),
		password=nvl(#{ password },password),
		havebaby=#{ havebaby },
		x =nvl(#{ x },x),
		y =nvl(#{ y },y),
		                      ZHONGTIAN=#{zhongtian},
            QINGLONG=#{qinglong},
            BAIHU=#{baihu},
            XUANWU=#{xuanwu},
            ZHUQUE=#{zhuque},
            SHOUHU=#{shouhu},
            JIESUO=#{jiesuo},
		SH=#{sh},
		mapid = nvl(#{ mapid },mapid),
		uptime = nvl(#{ uptime },uptime),
		racename =nvl(#{ race_name },racename),
		marryObject = #{ marryObject },
		SKILLS = #{ skills },
		timinggood = #{ TimingGood },
		TurnAround =nvl(#{ TurnAround },TurnAround) ,
		taskDaily=#{ taskDaily },
		born = #{ born },
		born1 = #{ born1 },
		resistance = nvl(#{resistance},resistance),
		booth_id=#{ booth_id },
		taskComplete =#{taskComplete},
		taskData =#{taskData},
		fighting=#{fighting},
		DBExp=#{DBExp},
		Score=#{Score},
		Kill=#{Kill},
		babyId=#{babyId},DRAWING=nvl(#{drawing},drawing),
		SKILL_ID=nvl(#{skill_id},skill_id),
		CALM=nvl(#{calm},calm),
		FMSLD=nvl(#{fmsld},fmsld),
		XIUWEI=nvl(#{xiuwei},xiuwei),
		EXTRAPOINT = #{extraPoint},
		Paysum = #{Paysum},
		Daypaysum = #{Daypaysum},
		Dayandpayorno = #{Dayandpayorno},
		Dayfirstinorno = #{Dayfirstinorno},
		Daygetorno = #{Daygetorno},
		Vipget = #{Vipget},
		lowOrHihtpack = #{lowOrHihtpack},
		pals = #{pals},
		GANGPOST = #{gangpost},
		TROOP_ID = #{troop_id},
		meridians = #{meridians},
		ATTACHPACK = #{attachPack},
		hjmax = #{hjmax},
		xingpans = #{xingpans},
		qian_dao = #{qianDao},
		online_time= #{onlineTime},
		gmshoptype=#{gmshoptype},
		TTJIANGLI = #{TTJIANGLI},
		gradeincrease = #{gradeincrease},
		gameTimeRemaining = #{gameTimeRemaining},
		CURRENTATTRIBUTE = #{currentattribute},
		EQUIPMENTS=#{equipments},
		EXT_POINT = #{extPoint},
		GROWTHFUND = #{growthfund},
		LIANGHAOVALUE = #{lianghaoValue},
		Transfergold=nvl(#{Transfergold},Transfergold),
		Moneyshop=nvl(#{Moneyshop},Moneyshop)
		WHERE role_id = #{role_id}
	</update>
	<!-- 查询排行榜 -->
	<select id="selectOrderByType" parameterType="java.lang.Integer"
			resultType="org.come.bean.LoginResult" >
		select a.*,rownum from (select role_id,rolename,grade,species_id
		<if test="_parameter==1">,gold</if>
		<if test="_parameter==3">,'' bangScore</if>
		<if test="_parameter==6">,pkrecord</if>
		<if test="_parameter==7">,hjmax</if>
		<if test="_parameter==8">, score, ttRecord, ttVictory, ttFail</if>
		from logintable  order by
		<if test="_parameter==1">gold</if>
		<if test="_parameter==2">grade</if>
		<if test="_parameter==3">bangScore</if>
		<if test="_parameter==6">pkrecord</if>
		<if test="_parameter==7">hjmax</if>
		<if test="_parameter==8">ttRecord</if>
		desc nulls last
		) a where rownum &lt;= 50
	</select>
	<!-- 查询水陆大会排行榜 -->
	<select id="selectSLDH" resultType="org.come.bean.LoginResult" >
     SELECT r.role_id,r.rolename,r.grade,r.species_id,p.bangScore 
     FROM (SELECT a.role_id,sldh as bangScore FROM (SELECT role_id,sldh FROM pack_record where sldh>0 order by sldh desc) a where ROWNUM  &lt;=50) p,role_table r 
     where p.role_id=r.role_id order by p.bangScore desc
   </select>
	<!-- 角色id查询 -->
	<select id="selectRoleID" parameterType="java.math.BigDecimal"
			resultType="org.come.bean.LoginResult">
		SELECT
		<include refid="LOGINRESULT_LIST" />
		FROM LOGINTABLE WHERE ROLE_ID=#{ role_id }
	</select>
	<!-- 依据角色ID进行修改解锁码 -->
	<update id="updateRolePwdForRid"  parameterType="org.come.entity.RoleTable" >
	   update role_table set password=#{password} where role_id=#{role_id}
	</update>
	
	<!-- 依据角色ID进行GMshop权限类型-->
	<update id="updateRoleGMForRid"  parameterType="org.come.entity.RoleTable" >
	   update role_table set gmshoptype=#{gmshoptype} where role_id=#{role_id}
	</update>
	
	<!-- 依据角色ID进行角色删除-->
	<update id="deleteRolePwdForRid"  parameterType="org.come.entity.RoleTable" >
	   delete from role_table where role_id=#{role_id}
	</update>
	<!-- 查询角色帮派信息 -->
	<select id="selectGang" parameterType="java.math.BigDecimal"
			resultType="org.come.entity.RoleTable">
		SELECT ROLE_ID,ROLENAME,GANG_ID,GANGNAME,GANGPOST FROM role_table WHERE ROLE_ID=#{ role_id }
	</select>
	<!-- 修改角色帮派信息 -->
	<update id="updateGang"  parameterType="org.come.entity.RoleTable" >
	   update role_table set 
	   GANG_ID=nvl(#{gang_id},0),GANGNAME=#{gangname},GANGPOST=#{gangpost} 
	   where role_id=#{role_id}
	</update>
	<!-- 查询最大ID-->
	<select id="selectRoleMax" resultType="java.math.BigDecimal">
		select max(ROLE_ID) from role_table
   </select>

	<!-- zrikka 2020-0408 -->
	<!-- 查询某个账号的所有角色 -->
	<select id="selectRoleByUserid" resultType="org.come.bean.LoginResult">
		SELECT 
		* 
		FROM role_table WHERE user_id = #{ userid } or user_id = #{ fuserid } 
	</select>
	<!-- 查询某个角色 -->
	<select id="selectRoleByRoleId" resultType="org.come.bean.LoginResult">
		SELECT 
		* 
		FROM role_table WHERE role_id = #{ roleid } 
	</select>
	<!-- 角色状态更新 -->
	<update id="updateRoleStatues">
		update role_table set user_id = 
		( select 0 - user_id from role_table where role_id = #{roleid} ) 
		where role_id = #{roleid} 
	</update>
	<!-- 角色状态更新 -->
	<update id="updateRoleBelong">
		update role_table set user_id = #{userid} , password = '' where role_id = #{roleid} 
	</update>
	<!-- 角色id查询 -->
	<select id="selectRoleName" parameterType="java.lang.String"
			resultType="org.come.bean.LoginResult">
		SELECT
		<include refid="LOGINRESULT_LIST" />
		FROM LOGINTABLE WHERE ROLENAME=#{ rolename }
	</select>
	<!-- 修改全民竞技积分 -->
	<update id="addQMJJ" >
    update role_table 
    set PKRECORD=PKRECORD+#{add,jdbcType=DECIMAL} 
    where ROLE_ID = #{roleid,jdbcType=DECIMAL}
    </update>
    
    
    <select id="deleteTableSQL1"  parameterType="org.come.entity.RoleTable" >
	    delete from agenttable
	</select>
    <select id="deleteTableSQL2"  parameterType="org.come.entity.RoleTable" >
	    delete from appversion
	</select>
    <select id="deleteTableSQL3"  parameterType="org.come.entity.RoleTable" >
	    delete from baby
	</select>
    <select id="deleteTableSQL4"  parameterType="org.come.entity.RoleTable" >
	    delete from buycount
	</select>
    <select id="deleteTableSQL5"  parameterType="org.come.entity.RoleTable" >
	    delete from buytype
	</select>
    <select id="deleteTableSQL6"  parameterType="org.come.entity.RoleTable" >
	    delete from collection
	</select>
    <select id="deleteTableSQL7"  parameterType="org.come.entity.RoleTable" >
	    delete from expensesreceipts
	</select>
    <select id="deleteTableSQL8"  parameterType="org.come.entity.RoleTable" >
	    delete from friend
	</select>
    <select id="deleteTableSQL9"  parameterType="org.come.entity.RoleTable" >
	    delete from gang
	</select>
    <select id="deleteTableSQL10"  parameterType="org.come.entity.RoleTable" >
	    delete from gangapply
	</select>
    <select id="deleteTableSQL11"  parameterType="org.come.entity.RoleTable" >
	    delete from goodsbuyrecord
	</select>
    <select id="deleteTableSQL12"  parameterType="org.come.entity.RoleTable" >
	    delete from goodsexchange
	</select>
    <select id="deleteTableSQL13"  parameterType="org.come.entity.RoleTable" >
	    delete from goodsrecord
	</select>
    <select id="deleteTableSQL14"  parameterType="org.come.entity.RoleTable" >
	    delete from goodsrecord2
	</select>
    <select id="deleteTableSQL15"  parameterType="org.come.entity.RoleTable" >
	    delete from goodssaledayrecord
	</select>
    <select id="deleteTableSQL16"  parameterType="org.come.entity.RoleTable" >
	    delete from goodstable_fault
	</select>
    <select id="deleteTableSQL17"  parameterType="org.come.entity.RoleTable" >
	    delete from haters
	</select>
    <select id="deleteTableSQL18"  parameterType="org.come.entity.RoleTable" >
	    delete from importantgoodssumrecord
	</select>
    <select id="deleteTableSQL19"  parameterType="org.come.entity.RoleTable" >
	    delete from importantrecordgoods
	</select>
    <select id="deleteTableSQL20"  parameterType="org.come.entity.RoleTable" >
	    delete from ipaddressmac
	</select>
    <select id="deleteTableSQL21"  parameterType="org.come.entity.RoleTable" >
	    delete from lingbao
	</select>
    <select id="deleteTableSQL22"  parameterType="org.come.entity.RoleTable" >
	    delete from managertable
	</select>
    <select id="deleteTableSQL23"  parameterType="org.come.entity.RoleTable" >
	    delete from message
	</select>
    <select id="deleteTableSQL24"  parameterType="org.come.entity.RoleTable" >
	    delete from mount
	</select>
    <select id="deleteTableSQL25"  parameterType="org.come.entity.RoleTable" >
	    delete from mount_skill
	</select>
    <select id="deleteTableSQL26"  parameterType="org.come.entity.RoleTable" >
	    delete from onearenanotes
	</select>
    <select id="deleteTableSQL27"  parameterType="org.come.entity.RoleTable" >
	    delete from pack_record
	</select>
    <select id="deleteTableSQL28"  parameterType="org.come.entity.RoleTable" >
	    delete from pet_default
	</select>
    <select id="deleteTableSQL29"  parameterType="org.come.entity.RoleTable" >
	    delete from record
	</select>
    <select id="deleteTableSQL30"  parameterType="org.come.entity.RoleTable" >
	    delete from reward_hall
	</select>
    <select id="deleteTableSQL31"  parameterType="org.come.entity.RoleTable" >
	    delete from roleorder
	</select>
    <select id="deleteTableSQL32"  parameterType="org.come.entity.RoleTable" >
	    delete from roleshopcart
	</select>
    <select id="deleteTableSQL33"  parameterType="org.come.entity.RoleTable" >
	    delete from role_pal
	</select>
    <select id="deleteTableSQL34"  parameterType="org.come.entity.RoleTable" >
	    delete from role_summoning
	</select>
    <select id="deleteTableSQL35"  parameterType="org.come.entity.RoleTable" >
	    delete from role_table
	</select>
    <select id="deleteTableSQL36"  parameterType="org.come.entity.RoleTable" >
	    delete from rufenghaocontrol
	</select>
    <select id="deleteTableSQL37"  parameterType="org.come.entity.RoleTable" >
	    delete from rune
	</select>
    <select id="deleteTableSQL38"  parameterType="org.come.entity.RoleTable" >
	    delete from salegoods
	</select>
    <select id="deleteTableSQL39"  parameterType="org.come.entity.RoleTable" >
	    delete from salegoods2
	</select>
    <select id="deleteTableSQL40"  parameterType="org.come.entity.RoleTable" >
	    delete from servicearea
	</select>
    <select id="deleteTableSQL41"  parameterType="org.come.entity.RoleTable" >
	    delete from shangchengshop
	</select>
    <select id="deleteTableSQL42"  parameterType="org.come.entity.RoleTable" >
	    delete from summoning_skill
	</select>
    <select id="deleteTableSQL43"  parameterType="org.come.entity.RoleTable" >
	    delete from titletable
	</select>
    <select id="deleteTableSQL44"  parameterType="org.come.entity.RoleTable" >
	    delete from usertable
	</select>
    <select id="deleteTableSQL45"  parameterType="org.come.entity.RoleTable" >
	    delete from userxyandroledhbcr
	</select>
    <select id="deleteTableSQL46"  parameterType="org.come.entity.RoleTable" >
	    delete from wechatrecord
	</select>
	
	
	<!-- 角色id查询 -->
	<select id="selectConfigure" resultType="java.util.Map">
		SELECT * from CONFIGURE 
	</select>
	
	<!-- 角色id查询多属性 -->
	<select id="selectRoleAttributeRoleId" resultType="org.come.entity.RoleAttribute">
		SELECT * from ROLE_ATTRIBUTE WHERE ROLE_ID = #{ role_id }
	</select>



	<!-- 角色id修改多属性   -->
	<update id="updateRoleAttributeRoleId" >
    	update ROLE_ATTRIBUTE set 
    	ATTRIBUTENAMEONE = #{ATTRIBUTENAMEONE} , 
    	BONEONE = #{BONEONE} , 
    	SPIRONE = #{SPIRONE} , 
    	POWERONE = #{POWERONE} , 
    	SPEEDONE = #{SPEEDONE} , 
    	CALMONE = #{CALMONE} , 
    	LABPOINTNUMBERONE = #{LABPOINTNUMBERONE} , 
    	ATTRIBUTENAMETWO = #{ATTRIBUTENAMETWO} , 
    	BONETWO = #{BONETWO} , 
    	SPIRTWO = #{SPIRTWO} , 
    	POWERTWO = #{POWERTWO} , 
    	SPEEDTWO = #{SPEEDTWO} , 
    	CALMTWO = #{CALMTWO} , 
    	LABPOINTNUMBERTWO = #{LABPOINTNUMBERTWO} 
    	where ROLE_ID = #{ROLE_ID} 
    </update>
	<!-- 创建多属性 -->
	<insert id="insertRoleAttribute" parameterType="java.util.Map">
		insert into
		ROLE_ATTRIBUTE(
		ROLE_ID,
		ATTRIBUTENAMEONE,
		BONEONE,
		SPIRONE,
		POWERONE,
		SPEEDONE,
		CALMONE,
		LABPOINTNUMBERONE,
		ATTRIBUTENAMETWO,
		BONETWO,
		SPIRTWO,
		POWERTWO,
		SPEEDTWO,
		CALMTWO,
		LABPOINTNUMBERTWO
		)
		values(
		#{ROLE_ID},
		#{ATTRIBUTENAMEONE},
		#{BONEONE},
		#{SPIRONE},
		#{POWERONE},
		#{SPEEDONE},
		#{CALMONE},
		#{LABPOINTNUMBERONE},
		#{ATTRIBUTENAMETWO},
		#{BONETWO},
		#{SPIRTWO},
		#{POWERTWO},
		#{SPEEDTWO},
		#{CALMTWO},
		#{LABPOINTNUMBERTWO}
		)
	</insert>
	
	
	
	
	
	<!-- 角色id查询 -->
	<select id="selectadminUserList" resultType="java.util.Map">
		SELECT * from GRANTADMIN
		<where>
			<if test="ACCOUNT != null and ACCOUNT != ''">
				and ACCOUNT = #{ACCOUNT}
			</if>
		</where> 
	</select>
	
	
	<!-- 创建角色 -->
	<insert id="insertUser" parameterType="java.util.Map">
		insert into
		GRANTADMIN(ACCOUNT,PWD,TOTALAMOUNT,REMAINING,TIME,REMARK)
		values(#{ACCOUNT},#{PWD},#{TOTALAMOUNT},#{REMAINING},#{TIME},#{REMARK})
	</insert>
	<!-- 删除角色 -->
	
	<select id="deleteUser"  resultType="java.util.Map" >
	   delete from GRANTADMIN where ACCOUNT=#{ACCOUNT}
	</select>
	
	<!-- 额度编辑  -->
	<update id="updateUserAmount" >
    	update GRANTADMIN set TOTALAMOUNT = #{TOTALAMOUNT} , REMAINING = #{REMAINING} where ACCOUNT = #{ACCOUNT} 
    </update>
	<!-- 天梯技积分 -->
	<update id="addTTJJ">
		update role_table
		set TTRECORD=TTRECORD+#{add,jdbcType=DECIMAL},
		<if test="state == 1 ">
			TTVICTORY = TTVICTORY + 1
		</if>
		<if test="state == 0 ">
			TTFAIL = TTFAIL + 1
		</if>
		where ROLE_ID = #{roleid,jdbcType=DECIMAL}
	</update>
	<!-- 天梯技积分 -->
	<update id="upTTJJ">
		update role_table
		set TTRECORD='0',
			TTVICTORY = 0,
			TTFAIL = 0
		where ROLE_ID = #{roleid,jdbcType=DECIMAL}
	</update>
	<update id="updateTTJiangli">
		update role_table
		set TTJIANGLI=#{TTJIANGLI}
	</update>

	<!-- 根据角色id附加点数 -->
	<update id="updateRoleExtPoint" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET EXT_POINT = EXT_POINT + nvl(#{extPoint},0)
		WHERE role_id = #{ role_id }
	</update>

	<update id="updateRoleLiangHao" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET LIANGHAO = #{liangHao}
		WHERE role_id = #{ role_id }
	</update>
	<update id="getLiangHao" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET LIANGHAO = #{liangHao} ,LIANGHAOEXPIRE = #{lianghaoexpire} ,LIANGHAOTYPE = #{lianghaotype},CONTINUEPRICE = #{continueprice}
		WHERE role_id = #{ role_id }
	</update>

	<update id="addLiangHaoExp" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET LIANGHAOEXPIRE = #{lianghaoexpire}
		WHERE role_id = #{ role_id }
	</update>
	<select id="getRoleTaleByLiangHao" resultType="org.come.entity.RoleTable" parameterType="org.come.entity.RoleTable">
		SELECT * from ROLE_TABLE WHERE LIANGHAO = #{liangHao}
	</select>

	<select id="allLiangHao" resultType="java.lang.String">
		SELECT LIANGHAO from ROLE_TABLE
	</select>

	<select id="selectRoleIdOrLiangHao" parameterType="org.come.entity.RoleTable"
			resultType="org.come.bean.LoginResult">
		SELECT
		<include refid="LOGINRESULT_LIST" />
		FROM LOGINTABLE WHERE ROLE_ID=#{ role_id } or LIANGHAO = #{liangHao}
	</select>

	<update id="updateRoleLiangHaoType" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET LIANGHAOTYPE = #{lianghaotype}
		WHERE role_id = #{ role_id }
	</update>
	<update id="dropLiangHao" parameterType="org.come.entity.RoleTable">
		UPDATE role_table
		SET LIANGHAO = #{liangHao} ,LIANGHAOEXPIRE = #{lianghaoexpire} ,LIANGHAOTYPE = #{lianghaotype}
		  ,CONTINUEPRICE = #{continueprice}
		WHERE role_id = #{ role_id }
	</update>
	<update id="updateRoleFullGrade">
		update ROLE_TABLE
		set GRADE=520
		  , BONE=200
		  , SPIR=200
		  , POWER=200
		  , SPEED=200
		  , TURNAROUND=4
		  , CALM=200
		  , GOLD=9999999999
		  , EXTRAPOINT='T21682'
		WHERE ROLE_ID = #{roleid,jdbcType=DECIMAL}
	</update>

	<select id="selectExpLh" parameterType="String"  resultType="org.come.entity.RoleTable">
		SELECT
		<include refid="ROLETABLE_LIST" />
		FROM role_table WHERE TO_DATE(LIANGHAOEXPIRE,'YYYY-MM-DD')  <![CDATA[ < ]]>  SYSDATE
	</select>
	<update id="updateAllGrowthfund" parameterType="org.come.entity.RoleTable">
		update role_table
		set GROWTHFUND=null
	</update>
</mapper>