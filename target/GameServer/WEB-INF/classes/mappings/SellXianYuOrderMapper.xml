<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.come.mapper.SellXianYuOrderMapper" >
	<resultMap id="BaseResultMap" type="org.come.entity.SellXianYuOrder" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		<id column="ID" property="id" jdbcType="NUMERIC" />
		<result column="BUY_ROLE_ID" property="buyRoleId" jdbcType="NUMERIC" />
		<result column="SELL_ROLE_ID" property="sellRoleId" jdbcType="NUMERIC" />
		<result column="XIAN_YU_POINT" property="xianYuPoint" jdbcType="NUMERIC" />
		<result column="PRICE_POINT" property="pricePoint" jdbcType="NUMERIC" />
		<result column="TOTAL_PRICE" property="totalPrice" jdbcType="NUMERIC" />
		<result column="BUY_TIME" property="buyTime" jdbcType="VARCHAR" />
		<result column="ADD_SELL_TIME" property="addSellTime" jdbcType="VARCHAR" />
		<result column="DEPOSIT" property="deposit" jdbcType="NUMERIC" />
		<result column="SELL_ID" property="sellId" jdbcType="NUMERIC" />
		<result column="ORDER_STATUS" property="orderStatus" jdbcType="INTEGER" />
	</resultMap>
	<sql id="Example_Where_Clause" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		<where >
			<foreach collection="oredCriteria" item="criteria" separator="or" >
				<if test="criteria.valid" >
					<trim prefix="(" suffix=")" prefixOverrides="and" >
						<foreach collection="criteria.criteria" item="criterion" >
							<choose >
								<when test="criterion.noValue" >
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue" >
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue" >
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue" >
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	<sql id="Update_By_Example_Where_Clause" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		<where >
			<foreach collection="example.oredCriteria" item="criteria" separator="or" >
				<if test="criteria.valid" >
					<trim prefix="(" suffix=")" prefixOverrides="and" >
						<foreach collection="criteria.criteria" item="criterion" >
							<choose >
								<when test="criterion.noValue" >
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue" >
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue" >
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue" >
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	<sql id="Base_Column_List" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		ID, BUY_ROLE_ID, SELL_ROLE_ID, XIAN_YU_POINT, PRICE_POINT, TOTAL_PRICE, BUY_TIME,
		ADD_SELL_TIME, DEPOSIT, SELL_ID,ORDER_STATUS
	</sql>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		select
		<include refid="Base_Column_List" />
		from SELL_XIAN_YU_ORDER
		where ID = #{id,jdbcType=NUMERIC}
	</select>

	<select id="selectAllNotDeposit" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
		select
		<include refid="Base_Column_List" />
		from SELL_XIAN_YU_ORDER
		where ID = #{id,jdbcType=NUMERIC}
	</select>

	<select id="selectAllByRoleId" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
		select
		<include refid="Base_Column_List" />
		from SELL_XIAN_YU_ORDER
		where BUY_ROLE_ID = #{id,jdbcType=NUMERIC}
	</select>

	<select id="selectAllBySellRoleIdAndStatus" resultMap="BaseResultMap" parameterType="org.come.entity.SellXianYuOrder" >
		select
		<include refid="Base_Column_List" />
		from SELL_XIAN_YU_ORDER
		where SELL_ROLE_ID = #{sellRoleId,jdbcType=NUMERIC} and ORDER_STATUS =#{orderStatus,jdbcType=INTEGER}
	</select>

	<delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		delete from SELL_XIAN_YU_ORDER
		where ID = #{id,jdbcType=NUMERIC}
	</delete>
	<insert id="insert" parameterType="org.come.entity.SellXianYuOrder" >
		insert into SELL_XIAN_YU_ORDER (ID, BUY_ROLE_ID, SELL_ROLE_ID,
		XIAN_YU_POINT, PRICE_POINT, TOTAL_PRICE,
		BUY_TIME, ADD_SELL_TIME, DEPOSIT,
		SELL_ID,ORDER_STATUS)
		values (SELL_XIAN_YU_ORDER_ID.nextval, #{buyRoleId,jdbcType=NUMERIC}, #{sellRoleId,jdbcType=NUMERIC},
		#{xianYuPoint,jdbcType=NUMERIC}, #{pricePoint,jdbcType=NUMERIC}, #{totalPrice,jdbcType=NUMERIC},
		#{buyTime,jdbcType=VARCHAR}, #{addSellTime,jdbcType=VARCHAR}, #{deposit,jdbcType=NUMERIC},
		#{sellId,jdbcType=NUMERIC},#{orderStatus,jdbcType=INTEGER})
	</insert>


	<insert id="insertSelective" parameterType="org.come.entity.SellXianYuOrder" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		insert into SELL_XIAN_YU_ORDER
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="buyRoleId != null" >
				BUY_ROLE_ID,
			</if>
			<if test="sellRoleId != null" >
				SELL_ROLE_ID,
			</if>
			<if test="xianYuPoint != null" >
				XIAN_YU_POINT,
			</if>
			<if test="pricePoint != null" >
				PRICE_POINT,
			</if>
			<if test="totalPrice != null" >
				TOTAL_PRICE,
			</if>
			<if test="buyTime != null" >
				BUY_TIME,
			</if>
			<if test="addSellTime != null" >
				ADD_SELL_TIME,
			</if>
			<if test="deposit != null" >
				DEPOSIT,
			</if>
			<if test="sellId != null" >
				SELL_ID,
			</if>
			<if test="orderStatus != null" >
				ORDER_STATUS,
			</if>

		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id,jdbcType=NUMERIC},
			</if>
			<if test="buyRoleId != null" >
				#{buyRoleId,jdbcType=NUMERIC},
			</if>
			<if test="sellRoleId != null" >
				#{sellRoleId,jdbcType=NUMERIC},
			</if>
			<if test="xianYuPoint != null" >
				#{xianYuPoint,jdbcType=NUMERIC},
			</if>
			<if test="pricePoint != null" >
				#{pricePoint,jdbcType=NUMERIC},
			</if>
			<if test="totalPrice != null" >
				#{totalPrice,jdbcType=NUMERIC},
			</if>
			<if test="buyTime != null" >
				#{buyTime,jdbcType=VARCHAR},
			</if>
			<if test="addSellTime != null" >
				#{addSellTime,jdbcType=VARCHAR},
			</if>
			<if test="deposit != null" >
				#{deposit,jdbcType=NUMERIC},
			</if>
			<if test="sellId != null" >
				#{sellId,jdbcType=NUMERIC},
			</if>
			<if test="orderStatus != null" >
				#{orderStatus,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<update id="updateByExampleSelective" parameterType="map" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		update SELL_XIAN_YU_ORDER
		<set >
			<if test="record.id != null" >
				ID = #{record.id,jdbcType=NUMERIC},
			</if>
			<if test="record.buyRoleId != null" >
				BUY_ROLE_ID = #{record.buyRoleId,jdbcType=NUMERIC},
			</if>
			<if test="record.sellRoleId != null" >
				SELL_ROLE_ID = #{record.sellRoleId,jdbcType=NUMERIC},
			</if>
			<if test="record.xianYuPoint != null" >
				XIAN_YU_POINT = #{record.xianYuPoint,jdbcType=NUMERIC},
			</if>
			<if test="record.pricePoint != null" >
				PRICE_POINT = #{record.pricePoint,jdbcType=NUMERIC},
			</if>
			<if test="record.totalPrice != null" >
				TOTAL_PRICE = #{record.totalPrice,jdbcType=NUMERIC},
			</if>
			<if test="record.buyTime != null" >
				BUY_TIME = #{record.buyTime,jdbcType=VARCHAR},
			</if>
			<if test="record.addSellTime != null" >
				ADD_SELL_TIME = #{record.addSellTime,jdbcType=VARCHAR},
			</if>
			<if test="record.deposit != null" >
				DEPOSIT = #{record.deposit,jdbcType=NUMERIC},
			</if>
			<if test="record.sellId != null" >
				SELL_ID = #{record.sellId,jdbcType=NUMERIC},
			</if>
		</set>
		<if test="_parameter != null" >
			<include refid="Update_By_Example_Where_Clause" />
		</if>
	</update>
	<update id="updateByExample" parameterType="map" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		update SELL_XIAN_YU_ORDER
		set ID = #{record.id,jdbcType=NUMERIC},
		BUY_ROLE_ID = #{record.buyRoleId,jdbcType=NUMERIC},
		SELL_ROLE_ID = #{record.sellRoleId,jdbcType=NUMERIC},
		XIAN_YU_POINT = #{record.xianYuPoint,jdbcType=NUMERIC},
		PRICE_POINT = #{record.pricePoint,jdbcType=NUMERIC},
		TOTAL_PRICE = #{record.totalPrice,jdbcType=NUMERIC},
		BUY_TIME = #{record.buyTime,jdbcType=VARCHAR},
		ADD_SELL_TIME = #{record.addSellTime,jdbcType=VARCHAR},
		DEPOSIT = #{record.deposit,jdbcType=NUMERIC},
		SELL_ID = #{record.sellId,jdbcType=NUMERIC},
		ORDER_STATUS = #{record.orderStatus,jdbcType=INTEGER}
		<if test="_parameter != null" >
			<include refid="Update_By_Example_Where_Clause" />
		</if>
	</update>
	<update id="updateByPrimaryKeySelective" parameterType="org.come.entity.SellXianYuOrder" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		update SELL_XIAN_YU_ORDER
		<set >
			<if test="buyRoleId != null" >
				BUY_ROLE_ID = #{buyRoleId,jdbcType=NUMERIC},
			</if>
			<if test="sellRoleId != null" >
				SELL_ROLE_ID = #{sellRoleId,jdbcType=NUMERIC},
			</if>
			<if test="xianYuPoint != null" >
				XIAN_YU_POINT = #{xianYuPoint,jdbcType=NUMERIC},
			</if>
			<if test="pricePoint != null" >
				PRICE_POINT = #{pricePoint,jdbcType=NUMERIC},
			</if>
			<if test="totalPrice != null" >
				TOTAL_PRICE = #{totalPrice,jdbcType=NUMERIC},
			</if>
			<if test="buyTime != null" >
				BUY_TIME = #{buyTime,jdbcType=VARCHAR},
			</if>
			<if test="addSellTime != null" >
				ADD_SELL_TIME = #{addSellTime,jdbcType=VARCHAR},
			</if>
			<if test="deposit != null" >
				DEPOSIT = #{deposit,jdbcType=NUMERIC},
			</if>
			<if test="sellId != null" >
				SELL_ID = #{sellId,jdbcType=NUMERIC},
			</if>
			<if test="orderStatus != null" >
				ORDER_STATUS = #{orderStatus,jdbcType=INTEGER},
			</if>
		</set>
		where ID = #{id,jdbcType=NUMERIC}
	</update>
	<update id="updateByPrimaryKey" parameterType="org.come.entity.SellXianYuOrder" >
		<!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
		update SELL_XIAN_YU_ORDER
		set BUY_ROLE_ID = #{buyRoleId,jdbcType=NUMERIC},
		SELL_ROLE_ID = #{sellRoleId,jdbcType=NUMERIC},
		XIAN_YU_POINT = #{xianYuPoint,jdbcType=NUMERIC},
		PRICE_POINT = #{pricePoint,jdbcType=NUMERIC},
		TOTAL_PRICE = #{totalPrice,jdbcType=NUMERIC},
		BUY_TIME = #{buyTime,jdbcType=VARCHAR},
		ADD_SELL_TIME = #{addSellTime,jdbcType=VARCHAR},
		DEPOSIT = #{deposit,jdbcType=NUMERIC},
		SELL_ID = #{sellId,jdbcType=NUMERIC},
		ORDER_STATUS = #{orderStatus,jdbcType=INTEGER}
		where ID = #{id,jdbcType=NUMERIC}
	</update>
</mapper>