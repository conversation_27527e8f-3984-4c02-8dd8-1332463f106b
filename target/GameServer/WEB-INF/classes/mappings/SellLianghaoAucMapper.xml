<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.come.mapper.SellLianghaoAucMapper" >
  <resultMap id="BaseResultMap" type="org.come.entity.SellLianghaoAuc" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="ID" property="id" jdbcType="NUMERIC" />
    <result column="BUY_ROLE_ID" property="buyRoleId" jdbcType="NUMERIC" />
    <result column="AUC_POINT" property="aucPoint" jdbcType="NUMERIC" />
    <result column="LIANGHAO" property="lianghao" jdbcType="VARCHAR" />
    <result column="BUY_TIME" property="buyTime" jdbcType="VARCHAR" />
    <result column="AUC_END_TIME" property="aucEndTime" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="NUMERIC" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ID, BUY_ROLE_ID, AUC_POINT, LIANGHAO, BUY_TIME, AUC_END_TIME, STATUS
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="org.come.entity.SellLianghaoAucExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from SELL_LIANGHAO_AUC
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from SELL_LIANGHAO_AUC
    where ID = #{id,jdbcType=NUMERIC}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from SELL_LIANGHAO_AUC
    where ID = #{id,jdbcType=NUMERIC}
  </delete>
  <delete id="deleteByExample" parameterType="org.come.entity.SellLianghaoAucExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from SELL_LIANGHAO_AUC
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="org.come.entity.SellLianghaoAuc" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into SELL_LIANGHAO_AUC (ID, BUY_ROLE_ID, AUC_POINT, 
      LIANGHAO, BUY_TIME, AUC_END_TIME, 
      STATUS,ORIGINALPRICE)
    values (SELL_LIANGHAO_AUC_ID.nextval, #{buyRoleId,jdbcType=NUMERIC}, #{aucPoint,jdbcType=NUMERIC},
      #{lianghao,jdbcType=VARCHAR}, #{buyTime,jdbcType=VARCHAR}, #{aucEndTime,jdbcType=VARCHAR}, 
      #{status,jdbcType=NUMERIC},#{originalprice,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" parameterType="org.come.entity.SellLianghaoAuc" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into SELL_LIANGHAO_AUC
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="buyRoleId != null" >
        BUY_ROLE_ID,
      </if>
      <if test="aucPoint != null" >
        AUC_POINT,
      </if>
      <if test="lianghao != null" >
        LIANGHAO,
      </if>
      <if test="buyTime != null" >
        BUY_TIME,
      </if>
      <if test="aucEndTime != null" >
        AUC_END_TIME,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=NUMERIC},
      </if>
      <if test="buyRoleId != null" >
        #{buyRoleId,jdbcType=NUMERIC},
      </if>
      <if test="aucPoint != null" >
        #{aucPoint,jdbcType=NUMERIC},
      </if>
      <if test="lianghao != null" >
        #{lianghao,jdbcType=VARCHAR},
      </if>
      <if test="buyTime != null" >
        #{buyTime,jdbcType=VARCHAR},
      </if>
      <if test="aucEndTime != null" >
        #{aucEndTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="org.come.entity.SellLianghaoAucExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from SELL_LIANGHAO_AUC
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update SELL_LIANGHAO_AUC
    <set >
      <if test="record.id != null" >
        ID = #{record.id,jdbcType=NUMERIC},
      </if>
      <if test="record.buyRoleId != null" >
        BUY_ROLE_ID = #{record.buyRoleId,jdbcType=NUMERIC},
      </if>
      <if test="record.aucPoint != null" >
        AUC_POINT = #{record.aucPoint,jdbcType=NUMERIC},
      </if>
      <if test="record.lianghao != null" >
        LIANGHAO = #{record.lianghao,jdbcType=VARCHAR},
      </if>
      <if test="record.buyTime != null" >
        BUY_TIME = #{record.buyTime,jdbcType=VARCHAR},
      </if>
      <if test="record.aucEndTime != null" >
        AUC_END_TIME = #{record.aucEndTime,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        STATUS = #{record.status,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update SELL_LIANGHAO_AUC
    set ID = #{record.id,jdbcType=NUMERIC},
      BUY_ROLE_ID = #{record.buyRoleId,jdbcType=NUMERIC},
      AUC_POINT = #{record.aucPoint,jdbcType=NUMERIC},
      LIANGHAO = #{record.lianghao,jdbcType=VARCHAR},
      BUY_TIME = #{record.buyTime,jdbcType=VARCHAR},
      AUC_END_TIME = #{record.aucEndTime,jdbcType=VARCHAR},
      STATUS = #{record.status,jdbcType=NUMERIC}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.come.entity.SellLianghaoAuc" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update SELL_LIANGHAO_AUC
    <set >
      <if test="buyRoleId != null" >
        BUY_ROLE_ID = #{buyRoleId,jdbcType=NUMERIC},
      </if>
      <if test="aucPoint != null" >
        AUC_POINT = #{aucPoint,jdbcType=NUMERIC},
      </if>
      <if test="lianghao != null" >
        LIANGHAO = #{lianghao,jdbcType=VARCHAR},
      </if>
      <if test="buyTime != null" >
        BUY_TIME = #{buyTime,jdbcType=VARCHAR},
      </if>
      <if test="aucEndTime != null" >
        AUC_END_TIME = #{aucEndTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=NUMERIC},
      </if>
    </set>
    where ID = #{id,jdbcType=NUMERIC}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.come.entity.SellLianghaoAuc" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update SELL_LIANGHAO_AUC
    set BUY_ROLE_ID = #{buyRoleId,jdbcType=NUMERIC},
      AUC_POINT = #{aucPoint,jdbcType=NUMERIC},
      LIANGHAO = #{lianghao,jdbcType=VARCHAR},
      BUY_TIME = #{buyTime,jdbcType=VARCHAR},
      AUC_END_TIME = #{aucEndTime,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=NUMERIC}
    where ID = #{id,jdbcType=NUMERIC}
  </update>


  <select id="selectByPrice" resultMap="BaseResultMap" parameterType="org.come.entity.SellLianghaoAuc" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from SELL_LIANGHAO_AUC
    where AUC_POINT <![CDATA[ > ]]>  #{aucPoint,jdbcType=NUMERIC} and STATUS = #{status,jdbcType=NUMERIC} and LIANGHAO = #{lianghao,jdbcType=VARCHAR}
  </select>

</mapper>