<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.come.mapper.IpaddressmacMapper" >
  <resultMap id="BaseResultMap" type="org.come.entity.Ipaddressmac" >
    <result column="IPID" property="ipid" jdbcType="DECIMAL" />
    <result column="ADDRESSKEY" property="addresskey" jdbcType="VARCHAR" />
    <result column="CTIME" property="ctime" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="org.come.entity.Ipaddressmac" >
    insert into IPADDRESSMAC (IPID, ADDRESSKEY, CTIME
      )
    values (#{ipid,jdbcType=DECIMAL}, #{addresskey,jdbcType=VARCHAR}, #{ctime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.come.entity.Ipaddressmac" >
    insert into IPADDRESSMAC
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ipid != null" >
        IPID,
      </if>
      <if test="addresskey != null" >
        ADDRESSKEY,
      </if>
      <if test="ctime != null" >
        CTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ipid != null" >
        #{ipid,jdbcType=DECIMAL},
      </if>
      <if test="addresskey != null" >
        #{addresskey,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  	<!--查询 -->
	<select id="selectIpaddressmac" resultType="org.come.entity.Ipaddressmac">
		select * from IPADDRESSMAC where addresskey = #{ addresskey }
	</select>
</mapper>