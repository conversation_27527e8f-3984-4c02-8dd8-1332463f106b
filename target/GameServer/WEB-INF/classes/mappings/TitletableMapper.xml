<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.come.mapper.TitletableMapper">

	<!-- 根据角色id查找角色称谓 -->
	<select id="selectRoleAllTitle" parameterType="java.math.BigDecimal"
			resultType="org.come.entity.Titletable">
	SELECT TITLEID,
	ROLEID,
	TITLENAME,RECORDTIME,LIMITTIME FROM TITLETABLE WHERE ROLEID = #{ roleid } and (RECORDTIME+LIMITTIME/(24*60) > sysdate or LIMITTIME = -1)
	</select>

	<!-- 添加角色称谓 -->
	<insert id="createRoleTitle" parameterType="org.come.entity.Titletable">
		<selectKey keyProperty="titleid" resultType="java.math.BigDecimal" order="BEFORE">
			select seq_titletable_id.nextval from dual
		</selectKey>
		insert into TITLETABLE values(#{titleid},#{roleid},#{titlename},#{recordTime,jdbcType=TIMESTAMP},#{limitTime,jdbcType=DECIMAL})
	</insert>

	<!-- 添加角色称谓 -->
	<update id="updateByPrimaryKey" parameterType="org.come.entity.Titletable" >
    UPDATE TITLETABLE
    SET titleid = #{titleid,jdbcType=DECIMAL},
      roleid = #{roleid,jdbcType=DECIMAL},
      limittime = #{limittime,jdbcType=DECIMAL},
      recordtime = #{recordtime,jdbcType=TIMESTAMP},
      titlename = #{titlename,jdbcType=VARCHAR}
    where titleid = #{titleid,jdbcType=DECIMAL}
  </update>

	<!-- 根据角色id和称谓名称查找角色称谓 -->
	<select id="selectRoleTitle"
			resultType="org.come.entity.Titletable">
		SELECT TITLEID,
		ROLEID,
		LIMITTIME,
		RECORDTIME,
		TITLENAME FROM TITLETABLE WHERE ROLEID = #{ roleid } AND TITLENAME = #{ titlename }
	</select>

</mapper>