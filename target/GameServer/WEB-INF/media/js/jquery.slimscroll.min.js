/*! Copyright (c) 2011 Piotr <PERSON> (http://rocha.la)
 * Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
 * and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
 *
 * Version: 1.1.1
 *
 */
(function(a){jQuery.fn.extend({slimScroll:function(b){var c={width:"auto",height:"250px",size:"7px",color:"#000",position:"right",distance:"1px",start:"top",opacity:0.4,alwaysVisible:false,disableFadeOut:false,railVisible:false,railColor:"#333",railOpacity:0.2,railDraggable:true,railClass:"slimScrollRail",barClass:"slimScrollBar",wrapperClass:"slimScrollDiv",allowPageScroll:false,wheelStep:20,touchScrollStep:200};var d=a.extend(c,b);this.each(function(){var w,r,k,p,z,s,o,j,l="<div></div>",t=30,q=false;var A=a(this);if(A.parent().hasClass(d.wrapperClass)){var h=A.scrollTop();u=A.parent().find("."+d.barClass);e=A.parent().find("."+d.railClass);B();if(a.isPlainObject(b)){if("height" in b&&b.height=="auto"){A.parent().css("height","auto");A.css("height","auto");var n=A.parent().parent().innerHeight();A.parent().css("height",n);A.css("height",n)}if("scrollTo" in b){h=parseInt(d.scrollTo)}else{if("scrollBy" in b){h+=parseInt(d.scrollBy)}else{if("destroy" in b){u.remove();e.remove();A.unwrap();return}}}v(h,false,true)}return}d.height=(d.height=="auto")?A.parent().innerHeight():d.height;var i=a(l).addClass(d.wrapperClass).css({position:"relative",overflow:"hidden",width:d.width,height:d.height});A.css({overflow:"hidden",width:d.width,height:d.height});var e=a(l).addClass(d.railClass).css({width:d.size,height:"100%",position:"absolute",top:0,display:(d.alwaysVisible&&d.railVisible)?"block":"none","border-radius":d.size,background:d.railColor,opacity:d.railOpacity,zIndex:90});var u=a(l).addClass(d.barClass).css({background:d.color,width:d.size,position:"absolute",top:0,opacity:d.opacity,display:d.alwaysVisible?"block":"none","border-radius":d.size,BorderRadius:d.size,MozBorderRadius:d.size,WebkitBorderRadius:d.size,zIndex:99});var f=(d.position=="right")?{right:d.distance}:{left:d.distance};e.css(f);u.css(f);A.wrap(i);A.parent().append(u);A.parent().append(e);if(d.railDraggable){u.draggable({axis:"y",containment:"parent",start:function(){k=true},stop:function(){k=false;m()},drag:function(C){v(0,a(this).position().top,false)}})}e.hover(function(){g()},function(){m()});u.hover(function(){r=true},function(){r=false});A.hover(function(){w=true;g();m()},function(){w=false;m()});A.bind("touchstart",function(D,C){if(D.originalEvent.touches.length){z=D.originalEvent.touches[0].pageY}});A.bind("touchmove",function(D){D.originalEvent.preventDefault();if(D.originalEvent.touches.length){var C=(z-D.originalEvent.touches[0].pageY)/d.touchScrollStep;z=D.originalEvent.touches[0].pageY;v(C,true)}});if(d.start==="bottom"){u.css({top:A.outerHeight()-u.outerHeight()});v(0,true)}else{if(d.start!=="top"){v(a(d.start).position().top,null,true);if(!d.alwaysVisible){u.hide()}}}x();B();function y(D){if(!w){return}var D=D||window.event;var E=0;if(D.wheelDelta){E=-D.wheelDelta/120}if(D.detail){E=D.detail/3}var C=D.target||D.srcTarget||D.srcElement;if(a(C).closest("."+d.wrapperClass).is(A.parent())){v(E,true)}if(D.preventDefault&&!q){D.preventDefault()}if(!q){D.returnValue=false}}function v(H,E,C){var G=H;var F=A.outerHeight()-u.outerHeight();if(E){G=parseInt(u.css("top"))+H*parseInt(d.wheelStep)/100*u.outerHeight();G=Math.min(Math.max(G,0),F);G=(H>0)?Math.ceil(G):Math.floor(G);u.css({top:G+"px"})}o=parseInt(u.css("top"))/(A.outerHeight()-u.outerHeight());G=o*(A[0].scrollHeight-A.outerHeight());if(C){G=H;var D=G/A[0].scrollHeight*A.outerHeight();D=Math.min(Math.max(D,0),F);u.css({top:D+"px"})}A.scrollTop(G);A.trigger("slimscrolling",~~G);g();m()}function x(){if(window.addEventListener){this.addEventListener("DOMMouseScroll",y,false);this.addEventListener("mousewheel",y,false)}else{document.attachEvent("onmousewheel",y)}}function B(){s=Math.max((A.outerHeight()/A[0].scrollHeight)*A.outerHeight(),t);u.css({height:s+"px"});var C=s==A.outerHeight()?"none":"block";u.css({display:C})}function g(){B();clearTimeout(p);if(o==~~o){q=d.allowPageScroll;if(j!=o){var C=(~~o==0)?"top":"bottom";A.trigger("slimscroll",C)}}j=o;if(s>=A.outerHeight()){q=true;return}u.stop(true,true).fadeIn("fast");if(d.railVisible){e.stop(true,true).fadeIn("fast")}}function m(){if(!d.alwaysVisible){p=setTimeout(function(){if(!(d.disableFadeOut&&w)&&!r&&!k){u.fadeOut("slow");e.fadeOut("slow")}},1000)}}});return this}});jQuery.fn.extend({slimscroll:jQuery.fn.slimScroll})})(jQuery);