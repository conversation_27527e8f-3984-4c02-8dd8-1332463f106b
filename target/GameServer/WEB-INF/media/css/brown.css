/***
<PERSON> theme
***/
/***
Reset and overrides  
***/
body {
  background-color: #623f18 !important;
}
/***
Page header
***/
.header .navbar-inner {
  filter: none !important;
  background-image: none !important;
  background-color: #35220d !important;
}
.header .btn-navbar {
  background-color: #35220d !important;
}
.header .nav .dropdown-toggle:hover,
.header .nav .dropdown.open .dropdown-toggle {
  background-color: #5a3a16 !important;
}
.header .nav li.dropdown .dropdown-toggle i {
  color: #d18d42 !important;
}
/***
Page sidebar
***/
.page-sidebar {
  background-color: #623f18;
}
ul.page-sidebar-menu > li > a {
  border-top: 1px solid #935f24 !important;
  color: #ffffff !important;
}
ul.page-sidebar-menu > li:last-child > a {
  border-bottom: 1px solid transparent !important;
}
ul.page-sidebar-menu > li a i {
  color: #9a6d3a;
}
ul.page-sidebar-menu > li.open > a,
ul.page-sidebar-menu > li > a:hover,
ul.page-sidebar-menu > li:hover > a {
  background: #4e3112;
}
ul.page-sidebar-menu > li.active > a {
  background: #4e3112 !important;
  border-top-color: transparent !important;
  color: #ffffff;
}
ul.page-sidebar-menu > li.active > a i {
  color: #ffffff;
}
ul.page-sidebar-menu > li > ul.sub-menu > li:first-child > a {
  border-top: 0px !important;
}
ul.page-sidebar-menu > li > ul.sub-menu > li.active > a,
ul.page-sidebar-menu > li > ul.sub-menu > li > a:hover {
  color: #ffffff !important;
  background: #8b5922 !important;
}
ul.page-sidebar-menu > li > ul.sub-menu > li > a:hover {
  background: #8b5922 !important;
}
/* 3rd level sub menu */
ul.page-sidebar-menu > li > ul.sub-menu li > ul.sub-menu > li.active > a,
ul.page-sidebar-menu > li > ul.sub-menu li > ul.sub-menu > li > a:hover,
ul.page-sidebar-menu > li > ul.sub-menu li.open > a {
  color: #ffffff !important;
  background: #8b5922 !important;
}
/* font color for all sub menu links*/
ul.page-sidebar-menu li > ul.sub-menu > li > a {
  color: #e5bf94;
}
/* menu arrows */
ul.page-sidebar-menu > li > a .arrow:before,
ul.page-sidebar-menu > li > a .arrow.open:before {
  color: #c88131 !important;
}
ul.page-sidebar-menu > li > ul.sub-menu a .arrow:before,
ul.page-sidebar-menu > li > ul.sub-menu a .arrow.open:before {
  color: #b4742c !important;
}
ul.page-sidebar-menu > li > a > .arrow.open:before {
  color: #d18d42 !important;
}
ul.page-sidebar-menu > li.active > a .arrow:before,
ul.page-sidebar-menu > li.active > a .arrow.open:before {
  color: #ffffff !important;
}
/* sidebar search */
.page-sidebar .sidebar-search input {
  background-color: #39250e !important;
  color: #b18d65;
}
.page-sidebar .sidebar-search input::-webkit-input-placeholder {
  color: #b18d65 !important;
}
.page-sidebar .sidebar-search input:-moz-placeholder {
  color: #b18d65 !important;
}
.page-sidebar .sidebar-search input:-ms-input-placeholder {
  color: #b18d65 !important;
}
.page-sidebar .sidebar-search input {
  background-color: #623f18 !important;
  color: #b18d65 !important;
}
.page-sidebar .sidebar-search .input-box {
  border-bottom: 1px solid #845f36 !important;
}
.page-sidebar .sidebar-search .submit {
  background-image: url(../../img/search-icon-brown.png);
}
/***
Sidebar toggler
***/
.sidebar-toggler {
  background-image: url(../../img/sidebar-toggler-brown.jpg);
  background-color: #39250e;
}
/* search box bg color on expanded */
.page-sidebar-closed .page-sidebar .sidebar-search.open {
  background-color: #623f18 !important;
}
.page-sidebar-closed .page-sidebar .sidebar-search.open .remove {
  background-image: url("../../img/sidebar-search-close-brown.png");
}
/* sub menu bg color on hover menu item */
.page-sidebar-closed ul.page-sidebar-menu > li:hover .sub-menu {
  background-color: #623f18;
}
/***
Horizontal Menu(new in v1.2)
***/
/*search*/
.header .hor-menu .hor-menu-search-form-toggler.hide {
  background: #000000 url(../../img/hor-menu-search-close.png) no-repeat center;
}
.header .hor-menu .search-form {
  background: #000000;
}
.header .hor-menu .search-form .btn {
  color: #efd7bd;
  background: #000000 url(../../img/search-icon.png) no-repeat center;
}
.header .hor-menu .search-form form input {
  color: #efd7bd;
}
.header .hor-menu .search-form form input::-webkit-input-placeholder {
  /* WebKit browsers */

  color: #efd7bd;
}
.header .hor-menu .search-form form input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */

  color: #efd7bd;
}
.header .hor-menu .search-form form input::-moz-placeholder {
  /* Mozilla Firefox 19+ */

  color: #efd7bd;
}
.header .hor-menu .search-form form input:-ms-input-placeholder {
  /* Internet Explorer 10+ */

  color: #efd7bd;
}
/***
Footer 
***/
.footer .footer-inner {
  color: #999999;
}
.footer .footer-tools .go-top {
  background-color: #7f511f;
}
.footer .footer-tools .go-top:hover {
  opacity: 0.7;
  filter: alpha(opacity=70);
}
.footer .footer-tools .go-top i {
  color: #d18d42;
}
/***
Footer Layouts (new in v1.3)
***/
/* begin:fixed footer */
.page-footer-fixed .footer {
  background-color: #39250e;
}
.page-footer-fixed .footer .footer-inner {
  color: #999999;
}
.page-footer-fixed .footer .footer-tools .go-top {
  background-color: #7f511f;
}
.page-footer-fixed .footer .footer-tools .go-top i {
  color: #d18d42;
}
/* end:fixed footer */
/***
Gritter Notifications 
***/
.gritter-top {
  background: url(../../plugins/gritter/images/gritter-brown.png) no-repeat left -30px !important;
}
.gritter-bottom {
  background: url(../../plugins/gritter/images/gritter-brown.png) no-repeat left bottom !important;
}
.gritter-item {
  display: block;
  background: url(../../plugins/gritter/images/gritter-brown.png) no-repeat left -40px !important;
}
.gritter-close {
  background: url(../../plugins/gritter/images/gritter-brown.png) no-repeat left top !important;
}
.gritter-title {
  text-shadow: none !important;
  /* Not supported by IE :( */

}
/* for the light (white) version of the gritter notice */
.gritter-light .gritter-item,
.gritter-light .gritter-bottom,
.gritter-light .gritter-top,
.gritter-light .gritter-close {
  background-image: url(../../plugins/gritter/images/gritter-light.png) !important;
}
.gritter-item-wrapper a {
  color: #b18d65;
}
.gritter-item-wrapper a:hover {
  color: #755a3b;
}
/* begin: boxed page */
@media (min-width: 980px) {
  .page-boxed {
    background-color: #492f12 !important;
  }
  .page-boxed .page-container {
    background-color: #623f18;
    border-left: 1px solid #976125;
    border-bottom: 1px solid #976125;
  }
  .page-boxed.page-sidebar-fixed .page-container {
    border-left: 0;
    border-bottom: 0;
  }
  .page-boxed.page-sidebar-fixed .page-sidebar {
    border-left: 1px solid #976125;
  }
  .page-boxed.page-sidebar-fixed.page-footer-fixed .footer {
    background-color: #492f12 !important;
  }
}
/* end: boxed page */
/***
Landscape phone to portrait tablet
***/
@media (max-width: 979px) {
  /***
    page sidebar
    ***/
  .page-sidebar {
    background-color: #412a10 !important;
  }
  ul.page-sidebar-menu > li > a {
    border-top: 1px solid #764c1d !important;
  }
  ul.page-sidebar-menu > li:last-child > a {
    border-bottom: 0 !important;
  }
  .page-sidebar .sidebar-search input {
    background-color: #412a10 !important;
  }
  ul.page-sidebar-menu > li.open > a,
  ul.page-sidebar-menu > li > a:hover,
  ul.page-sidebar-menu > li:hover > a {
    background: #311f0c;
  }
}
