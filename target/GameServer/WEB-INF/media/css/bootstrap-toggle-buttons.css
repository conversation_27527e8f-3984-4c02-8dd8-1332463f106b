/* line 11, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button {
  display: inline-block;
  cursor: pointer;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  position: relative;
  text-align: left;
  overflow: hidden;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/* line 29, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button.deactivate {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
  opacity: 0.5;
  cursor: default !important;
}
/* line 32, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button.deactivate label, .toggle-button.deactivate span {
  cursor: default !important;
}
/* line 36, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button > div {
  display: inline-block;
  width: 150px;
  position: absolute;
  top: 0;
}
/* line 41, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button > div.disabled {
  left: -50%;
}
/* line 45, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button input[type=checkbox] {
  display: none;
}
/* line 53, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span, .toggle-button label {
  cursor: pointer;
  position: relative;
  float: left;
  display: inline-block;
}
/* line 60, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button label {
  background: #fefefe;
  margin-left: -4px;
  margin-right: -4px;
  border: 1px solid #E6E6E6;
  margin-top: -1px;
  z-index: 100;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #fefefe), color-stop(100%, #e6e6e6));
  background-image: -webkit-linear-gradient(top, #fefefe, #e6e6e6);
  background-image: -moz-linear-gradient(top, #fefefe, #e6e6e6);
  background-image: -o-linear-gradient(top, #fefefe, #e6e6e6);
  background-image: linear-gradient(top, #fefefe, #e6e6e6);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}
/* line 72, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span {
  color: #fefefe;
  text-align: center;
  font-weight: bold;
  z-index: 1;
}
/* line 78, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.labelLeft {
  -moz-border-radius-topleft: 4px;
  -webkit-border-top-left-radius: 4px;
  border-top-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  -webkit-border-bottom-left-radius: 4px;
  border-bottom-left-radius: 4px;
  padding-left: 3px;
}
/* line 83, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.labelRight {
  -moz-border-radius-topright: 4px;
  -webkit-border-top-right-radius: 4px;
  border-top-right-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  -webkit-border-bottom-right-radius: 4px;
  border-bottom-right-radius: 4px;
  color: black;
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #fefefe), color-stop(100%, #e6e6e6));
  background-image: -webkit-linear-gradient(bottom, #fefefe, #e6e6e6);
  background-image: -moz-linear-gradient(bottom, #fefefe, #e6e6e6);
  background-image: -o-linear-gradient(bottom, #fefefe, #e6e6e6);
  background-image: linear-gradient(bottom, #fefefe, #e6e6e6);
  padding-right: 3px;
}
/* line 91, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.primary, .toggle-button span.labelLeft {
  color: #fefefe;
  background: #0088cc;
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #0088cc), color-stop(100%, #0055cc));
  background-image: -webkit-linear-gradient(bottom, #0088cc, #0055cc);
  background-image: -moz-linear-gradient(bottom, #0088cc, #0055cc);
  background-image: -o-linear-gradient(bottom, #0088cc, #0055cc);
  background-image: linear-gradient(bottom, #0088cc, #0055cc);
}
/* line 96, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.info {
  color: #fefefe;
  background: #5bc0de;
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #5bc0de), color-stop(100%, #2f96b4));
  background-image: -webkit-linear-gradient(bottom, #5bc0de, #2f96b4);
  background-image: -moz-linear-gradient(bottom, #5bc0de, #2f96b4);
  background-image: -o-linear-gradient(bottom, #5bc0de, #2f96b4);
  background-image: linear-gradient(bottom, #5bc0de, #2f96b4);
}
/* line 102, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.success {
  color: #fefefe;
  background: #62c462;
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #62c462), color-stop(100%, #51a351));
  background-image: -webkit-linear-gradient(bottom, #62c462, #51a351);
  background-image: -moz-linear-gradient(bottom, #62c462, #51a351);
  background-image: -o-linear-gradient(bottom, #62c462, #51a351);
  background-image: linear-gradient(bottom, #62c462, #51a351);
}
/* line 108, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.warning {
  color: #fefefe;
  background: #dbb450;
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #dbb450), color-stop(100%, #f89406));
  background-image: -webkit-linear-gradient(bottom, #dbb450, #f89406);
  background-image: -moz-linear-gradient(bottom, #dbb450, #f89406);
  background-image: -o-linear-gradient(bottom, #dbb450, #f89406);
  background-image: linear-gradient(bottom, #dbb450, #f89406);
}
/* line 114, ../sass/bootstrap-toggle-buttons.scss */
.toggle-button span.danger {
  color: #fefefe;
  background: #ee5f5b;
  background-image: -webkit-gradient(linear, 50% 100%, 50% 0%, color-stop(0%, #ee5f5b), color-stop(100%, #bd362f));
  background-image: -webkit-linear-gradient(bottom, #ee5f5b, #bd362f);
  background-image: -moz-linear-gradient(bottom, #ee5f5b, #bd362f);
  background-image: -o-linear-gradient(bottom, #ee5f5b, #bd362f);
  background-image: linear-gradient(bottom, #ee5f5b, #bd362f);
}
