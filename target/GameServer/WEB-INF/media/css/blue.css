/***
Blue theme
***/
/***
Reset and overrides  
***/
body {
  background-color: #1570a6 !important;
}
/***
Page header
***/
.header .navbar-inner {
  filter: none !important;
  background-image: none !important;
  background-color: #0f4e74 !important;
}
.header .btn-navbar {
  background-color: #0f4e74 !important;
}
.header .nav .dropdown-toggle:hover,
.header .nav .dropdown.open .dropdown-toggle {
  background-color: #146a9d !important;
}
.header .nav li.dropdown .dropdown-toggle i {
  color: #68bbec !important;
}
/***
Page sidebar
***/
.page-sidebar {
  background-color: #1570a6;
}
ul.page-sidebar-menu > li > a {
  border-top: 1px solid #1c95dc !important;
  color: #ffffff !important;
}
ul.page-sidebar-menu > li:last-child > a {
  border-bottom: 1px solid transparent !important;
}
ul.page-sidebar-menu > li a i {
  color: #7fc5ef;
}
ul.page-sidebar-menu > li.open > a,
ul.page-sidebar-menu > li > a:hover,
ul.page-sidebar-menu > li:hover > a {
  background: #12618f;
}
ul.page-sidebar-menu > li.active > a {
  background: #cc1d1d !important;
  border-top-color: transparent !important;
  color: #ffffff;
}
ul.page-sidebar-menu > li.active > a i {
  color: #ffffff;
}
ul.page-sidebar-menu > li > ul.sub-menu > li:first-child > a {
  border-top: 0px !important;
}
ul.page-sidebar-menu > li > ul.sub-menu > li.active > a,
ul.page-sidebar-menu > li > ul.sub-menu > li > a:hover {
  color: #ffffff !important;
  background: #1b8fd3 !important;
}
ul.page-sidebar-menu > li > ul.sub-menu > li > a:hover {
  background: #1b8fd3 !important;
}
/* 3rd level sub menu */
ul.page-sidebar-menu > li > ul.sub-menu li > ul.sub-menu > li.active > a,
ul.page-sidebar-menu > li > ul.sub-menu li > ul.sub-menu > li > a:hover,
ul.page-sidebar-menu > li > ul.sub-menu li.open > a {
  color: #ffffff !important;
  background: #1b8fd3 !important;
}
/* font color for all sub menu links*/
ul.page-sidebar-menu li > ul.sub-menu > li > a {
  color: #c3e4f7;
}
/* menu arrows */
ul.page-sidebar-menu > li > a .arrow:before,
ul.page-sidebar-menu > li > a .arrow.open:before {
  color: #51b1e9 !important;
}
ul.page-sidebar-menu > li > ul.sub-menu a .arrow:before,
ul.page-sidebar-menu > li > ul.sub-menu a .arrow.open:before {
  color: #3ba6e6 !important;
}
ul.page-sidebar-menu > li > a > .arrow.open:before {
  color: #68bbec !important;
}
ul.page-sidebar-menu > li.active > a .arrow:before,
ul.page-sidebar-menu > li.active > a .arrow.open:before {
  color: #ffffff !important;
}
/* sidebar search */
.page-sidebar .sidebar-search input {
  background-color: #0f5179 !important;
  color: #51b1e9;
}
.page-sidebar .sidebar-search input::-webkit-input-placeholder {
  color: #51b1e9 !important;
}
.page-sidebar .sidebar-search input:-moz-placeholder {
  color: #51b1e9 !important;
}
.page-sidebar .sidebar-search input:-ms-input-placeholder {
  color: #51b1e9 !important;
}
.page-sidebar .sidebar-search input {
  background-color: #1570a6 !important;
  color: #bfbfbf !important;
}
.page-sidebar .sidebar-search .input-box {
  border-bottom: 1px solid #51b1e9 !important;
}
.page-sidebar .sidebar-search .submit {
  background-image: url(../../img/search-icon-blue.png);
}
/***
Sidebar toggler
***/
.sidebar-toggler {
  background-image: url(../../img/sidebar-toggler-blue.jpg);
  background-color: #0f5179;
}
/* search box bg color on expanded */
.page-sidebar-closed .page-sidebar .sidebar-search.open {
  background-color: #1570a6 !important;
}
.page-sidebar-closed .page-sidebar .sidebar-search.open .remove {
  background-image: url("../../img/sidebar-search-close-blue.png");
}
/* sub menu bg color on hover menu item */
.page-sidebar-closed ul.page-sidebar-menu > li:hover .sub-menu {
  background-color: #1570a6;
}
/***
Horizontal Menu(new in v1.2)
***/
/*search*/
.header .hor-menu .hor-menu-search-form-toggler.hide {
  background: #000000 url(../../img/hor-menu-search-close.png) no-repeat center;
}
.header .hor-menu .search-form {
  background: #000000;
}
.header .hor-menu .search-form .btn {
  color: #f0f8fd;
  background: #04141e url(../../img/search-icon.png) no-repeat center;
}
.header .hor-menu .search-form form input {
  color: #f0f8fd;
}
.header .hor-menu .search-form form input::-webkit-input-placeholder {
  /* WebKit browsers */

  color: #f0f8fd;
}
.header .hor-menu .search-form form input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */

  color: #f0f8fd;
}
.header .hor-menu .search-form form input::-moz-placeholder {
  /* Mozilla Firefox 19+ */

  color: #f0f8fd;
}
.header .hor-menu .search-form form input:-ms-input-placeholder {
  /* Internet Explorer 10+ */

  color: #f0f8fd;
}
/***
Footer 
***/
.footer .footer-inner {
  color: #68bbec;
}
.footer .footer-tools .go-top {
  background-color: #1985c6;
}
.footer .footer-tools .go-top:hover {
  opacity: 0.7;
  filter: alpha(opacity=70);
}
.footer .footer-tools .go-top i {
  color: #68bbec;
}
/***
Footer Layouts (new in v1.3)
***/
/* begin:fixed footer */
.page-footer-fixed .footer {
  background-color: #0f5179;
}
.page-footer-fixed .footer .footer-inner {
  color: #68bbec;
}
.page-footer-fixed .footer .footer-tools .go-top {
  background-color: #1985c6;
}
.page-footer-fixed .footer .footer-tools .go-top i {
  color: #68bbec;
}
/* end:fixed footer */
/***
Gritter Notifications 
***/
.gritter-top {
  background: url(../../plugins/gritter/images/gritter-blue.png) no-repeat left -30px !important;
}
.gritter-bottom {
  background: url(../../plugins/gritter/images/gritter-blue.png) no-repeat left bottom !important;
}
.gritter-item {
  display: block;
  background: url(../../plugins/gritter/images/gritter-blue.png) no-repeat left -40px !important;
}
.gritter-close {
  background: url(../../plugins/gritter/images/gritter-blue.png) no-repeat left top !important;
}
.gritter-title {
  text-shadow: none !important;
  /* Not supported by IE :( */

}
/* for the light (white) version of the gritter notice */
.gritter-light .gritter-item,
.gritter-light .gritter-bottom,
.gritter-light .gritter-top,
.gritter-light .gritter-close {
  background-image: url(../../plugins/gritter/images/gritter-light.png) !important;
}
.gritter-item-wrapper a {
  color: #18a5ed;
}
.gritter-item-wrapper a:hover {
  color: #0b6694;
}
/* begin: boxed page */
@media (min-width: 980px) {
  .page-boxed {
    background-color: #125e8b !important;
  }
  .page-boxed .page-container {
    background-color: #1570a6;
    border-left: 1px solid #1c98e1;
    border-bottom: 1px solid #1c98e1;
  }
  .page-boxed.page-sidebar-fixed .page-container {
    border-left: 0;
    border-bottom: 0;
  }
  .page-boxed.page-sidebar-fixed .page-sidebar {
    border-left: 1px solid #1c98e1;
  }
  .page-boxed.page-sidebar-fixed.page-footer-fixed .footer {
    background-color: #125e8b !important;
  }
}
/* end: boxed page */
/***
Landscape phone to portrait tablet
***/
@media (max-width: 979px) {
  /***
    page sidebar
    ***/
  .page-sidebar {
    background-color: #105882 !important;
  }
  ul.page-sidebar-menu > li > a {
    border-top: 1px solid #187fbd !important;
  }
  ul.page-sidebar-menu > li:last-child > a {
    border-bottom: 0 !important;
  }
  .page-sidebar .sidebar-search input {
    background-color: #105882 !important;
  }
  ul.page-sidebar-menu > li.open > a,
  ul.page-sidebar-menu > li > a:hover,
  ul.page-sidebar-menu > li:hover > a {
    background: #0e4b70;
  }
}
