/*
Version: 3.3.2 Timestamp: Mon Mar 25 12:14:18 PDT 2013
*/

/*
to hide search box
*/
.no-search-box .select2-search { 
    display: none !important;
}

.select2-container {
    position: relative;
    display: inline-block;
    /* inline-block for ie7 */
    zoom: 1;
    *display: inline;
    vertical-align: middle;
}

.select2-container,
.select2-drop,
.select2-search,
.select2-search input{
  /*
    Force border-box so that % widths fit the parent
    container without overlap because of margin/padding.

    More Info : http://www.quirksmode.org/css/box.html
  */
  -webkit-box-sizing: border-box; /* webkit */
   -khtml-box-sizing: border-box; /* konqueror */
     -moz-box-sizing: border-box; /* firefox */
      -ms-box-sizing: border-box; /* ie */
          box-sizing: border-box; /* css3 */
}

.select2-container .select2-choice {
    display: block;
    height: 29px;
    padding: 0 0 0 8px;
    padding-top: 3px;
    overflow: hidden;
    position: relative;

    border: 1px solid #e5e5e5;
    white-space: nowrap;
    line-height: 26px;
    color: #444;
    text-decoration: none;

    -webkit-border-radius: 4px;
       -moz-border-radius: 4px;
            border-radius: 4px;

    -webkit-background-clip: padding-box;
       -moz-background-clip: padding;
            background-clip: padding-box;

    -webkit-touch-callout: none;
      -webkit-user-select: none;
       -khtml-user-select: none;
         -moz-user-select: none;
          -ms-user-select: none;
              user-select: none;

    background-color: #ffffff;
}

.select2-container.select2-drop-above .select2-choice {
    border-bottom-color: #e5e5e5;

    -webkit-border-radius:0 0 4px 4px;
       -moz-border-radius:0 0 4px 4px;
            border-radius:0 0 4px 4px;
}

.select2-container.select2-dropdown-open.select2-drop-above .select2-choice {
    border-bottom-color: #999999 !important;
}

.select2-container .select2-choice span {
    margin-right: 26px;
    display: block;
    overflow: hidden;

    white-space: nowrap;

    -ms-text-overflow: ellipsis;
     -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
}


.select2-container .select2-choice abbr {
    display: block;
    width: 12px;
    height: 12px;
    position: absolute;
    right: 26px;
    top: 8px;

    font-size: 1px;
    text-decoration: none;

    border: 0;
    background: url('../image/select2.png') right top no-repeat;
    cursor: pointer;
    outline: 0;
}
.select2-container .select2-choice abbr:hover {
    background-position: right -11px;
    cursor: pointer;
}

.select2-drop-mask {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9991;
    background-color: #fff;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"; /* works in IE 8 */
        filter: "alpha(opacity=0)"; /* expected to work in IE 8 */
        filter: alpha(opacity=0); /* IE 4-7 */
}

.select2-drop {
    width: 100%;
    margin-top:-1px;
    position: absolute;
    z-index: 9992;
    top: 100%;

    background: #ffffff;
    color: #000;
    border: 1px solid #e5e5e5;
    border-top: 0;
}

.select2-drop.select2-drop-active {
    border: 1px solid #999999;
    border-top: 0;
}

.select2-drop.select2-drop-above {
    margin-top: 1px;
    border-top: 1px solid #e5e5e5;
    border-bottom: 0;
}

.select2-drop.select2-drop-active.select2-drop-above {
    border-top: 1px solid #999999;
    border-bottom: 0;
}

.select2-container .select2-choice div {
    display: block;
    width: 18px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    border-left: 1px solid #e5e5e5;
    background: #fff;
}

.select2-container .select2-choice div b {
    display: block;
    width: 100%;
    height: 100%;
    background: url('../image/select2.png') no-repeat 0 3px;
}

.select2-search {
    display: inline-block;
    width: 100%;
    min-height: 26px;
    margin: 0;
    padding-left: 4px;
    padding-right: 4px;

    position: relative;
    z-index: 9992;

    white-space: nowrap;
}

.select2-search-hidden {
    display: block;
    position: absolute;
    left: -10000px;
}

.select2-search input {

    width: 100%;
    height: auto !important;
    min-height: 26px;
    padding: 4px 20px 4px 5px;
    margin: 0;

    outline: 0;
    font-family: sans-serif;
    font-size: 1em;

    border: 1px solid #e5e5e5;
    -webkit-border-radius: 0;
       -moz-border-radius: 0;
            border-radius: 0;

    -webkit-box-shadow: none;
       -moz-box-shadow: none;
            box-shadow: none;

    background: #fff url('../image/select2.png') no-repeat 100% -22px;
    -webkit-appearance: none !important;   
    color: #333333;         
      outline: 0;
      height: 20px;
        padding: 6px 6px !important;
      line-height: 20px;
      font-size: 14px;
      font-weight: normal;
      vertical-align: top;  
        background-color: #ffffff;
      filter: none !important;
          -webkit-box-shadow: none !important;
             -moz-box-shadow: none !important;
                  box-shadow: none !important;
          -webkit-border-radius: 0px;
             -moz-border-radius: 0px;
                  border-radius: 0px;
}

.select2-search input:focus {
    
border-color: #999999;
  outline: 0 !important;
  /* IE6-9 */
  -webkit-box-shadow: none;
     -moz-box-shadow: none;
          box-shadow: none; 
}  

.select2-drop.select2-drop-above .select2-search input {
    margin-top: 4px;
}

.select2-search input.select2-active {
    background: #fff url('../image/select2-spinner.gif') no-repeat 100%;
}

.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
    border: 1px solid #999999 !important;
    outline: none;
}

.select2-dropdown-open .select2-choice {
    border-color: #999999 !important;
    border-bottom-color: transparent;
    background-color: #fff;
}

.select2-dropdown-open .select2-choice div {
    background: transparent;
    border-left: none;
    filter: none;
}
.select2-dropdown-open .select2-choice div b {
    background-position: -18px 1px;
}

/* results */
.select2-results {
    max-height: 200px;
    padding: 0 0 0 4px;
    margin: 4px 4px 4px 0;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

.select2-results ul.select2-result-sub {
    margin: 0;
}

.select2-results ul.select2-result-sub > li .select2-result-label { padding-left: 20px }
.select2-results ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 40px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 60px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 80px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 100px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 110px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 120px }

.select2-results li {
    list-style: none;
    display: list-item;
    background-image: none;
}

.select2-results li.select2-result-with-children > .select2-result-label {
    font-weight: bold;
}

.select2-results .select2-result-label {
    padding: 3px 7px 4px;
    margin: 0;
    cursor: pointer;

    min-height: 1em;

    -webkit-touch-callout: none;
      -webkit-user-select: none;
       -khtml-user-select: none;
         -moz-user-select: none;
          -ms-user-select: none;
              user-select: none;
}

.select2-results .select2-highlighted {
    background: #eee;
    color: #333;
}

.select2-results li em {
    background: #eee;
    font-style: normal;
}

.select2-results .select2-highlighted em {
    background: transparent;
}

.select2-results .select2-highlighted ul {
    background: white;
    color: #000;
}


.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
    background: #fff;
    display: list-item;
}

/*
disabled look for disabled choices in the results dropdown
*/
.select2-results .select2-disabled.select2-highlighted {
    color: #666;
    background: #f4f4f4;
    display: list-item;
    cursor: default;
}
.select2-results .select2-disabled {
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}

.select2-results .select2-selected {
    display: none;
}

.select2-more-results.select2-active {
    background: #f4f4f4 url('../image/select2-spinner.gif') no-repeat 100%;
}

.select2-more-results {
    background: #f4f4f4;
    display: list-item;
}

/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container.select2-container-disabled .select2-choice div {
    background-color: #f4f4f4;
    background-image: none;
    border-left: 0;
}

.select2-container.select2-container-disabled .select2-choice abbr {
    display: none
}


/* multiselect */

.select2-container-multi .select2-choices {
    height: auto !important;
    height: 1%;
    margin: 0;
    padding: 0;
    position: relative;

    border: 1px solid #e5e5e5;
    cursor: text;
    overflow: hidden;

    background-color: #ffffff;
}

.select2-locked {
  padding: 3px 5px 3px 5px !important;
}

.select2-container-multi .select2-choices {
    min-height: 26px;
}

.select2-container-multi.select2-drop-open .select2-choices,
.select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid #999999 !important;
    outline: none;
}
.select2-container-multi .select2-choices li {
    float: left;
    list-style: none;
}
.select2-container-multi .select2-choices .select2-search-field {
    margin: 0;
    padding: 0;
    white-space: nowrap;
}

.select2-container-multi .select2-choices .select2-search-field input {
    padding: 5px;
    margin: 1px 0;
    
    font-family: sans-serif;
    font-size: 100%;
    color: #666;
    outline: 0;
    border: 0;
    -webkit-box-shadow: none;
       -moz-box-shadow: none;
            box-shadow: none;
    background: transparent !important;
}

.select2-container-multi .select2-choices .select2-search-field input.select2-active {
    background: #fff url('../image/select2-spinner.gif') no-repeat 100% !important;
}

.select2-default {
    color: #999 !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 18px;
    margin: 6px 0 0px 5px;
    position: relative;

    line-height: 13px;
    color: #333;
    cursor: default;
    border: 1px solid #e5e5e5;

    /*
    -webkit-border-radius: 3px;
       -moz-border-radius: 3px;
            border-radius: 3px;

    -webkit-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
       -moz-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
            box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
    */

    -webkit-background-clip: padding-box;
       -moz-background-clip: padding;
            background-clip: padding-box;

    -webkit-touch-callout: none;
      -webkit-user-select: none;
       -khtml-user-select: none;
         -moz-user-select: none;
          -ms-user-select: none;
              user-select: none;

    background-color: #ffffff;
    /*
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0 );
    background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
    background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    background-image: -o-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    background-image: -ms-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    background-image: linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    */
}
.select2-container-multi .select2-choices .select2-search-choice span {
    cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
    background: #ffffff;
}

.select2-search-choice-close {
    display: block;
    width: 12px;
    height: 13px;
    position: absolute;
    right: 3px;
    top: 3px;
    font-size: 1px;
    outline: none;
    background: url('../image/select2.png') right top no-repeat;
}

.select2-container-multi .select2-search-choice-close {
    left: 3px;
}

.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
    background-position: right -11px;
}

/* disabled styles */
.select2-container-multi.select2-container-disabled .select2-choices{
    background-color: #ffffff;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 5px;
    border: 1px solid #ddd;
    background-image: none;
    background-color: #f4f4f4;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
    display: none;
}
/* end multiselect */


.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-match {
    text-decoration: underline;
}

.select2-offscreen {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px !important;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px !important;
}

/* Retina-ize icons */

@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 144dpi)  {
  .select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice div b {
      background-image: url('../image/select2x2.png') !important;
      background-repeat: no-repeat !important;
      background-size: 60px 40px !important;
  }
  .select2-search input {
      background-position: 100% -21px !important;
  }
}
