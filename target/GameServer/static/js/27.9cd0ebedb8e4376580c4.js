webpackJsonp([27],{"N+b2":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("spLH"),r=(a("eNfa"),a("VsUZ")),l={name:"comment",components:{pagination:s.a},data:function(){return{userName:"",SafeNumber:"",userpwd:"",type:"",tableData:[],pager:{totalCount:0,currentPage:1,pageSize:20}}},methods:{coverFlag:function(e,t){return"0"==e[t.property]?"正常":"封号"},sendCommand:function(){var e=this,t={userName:this.userName,SafeNumber:this.SafeNumber,userpwd:this.userpwd};Object(r.g)(t).then(function(t){"1"==t?(e.$message.success("操作成功"),e.search()):"2"==t?e.$message.error("用户名不存在"):"3"==t?e.$message.error("安全码错误"):e.$message.error("操作失败")})},search:function(){var e=this;""==e.userName?this.type="all":this.type="one";var t={type:this.type,userName:this.userName};Object(r.j)(t).then(function(t){""==t?e.$message.warning("用户名不存在"):e.tableData=t})},initData:function(){var e=this;Object(r.j)({type:"all"}).then(function(t){null!=t&&(e.tableData=t)})}},mounted:function(){this.initData()}},n={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cm-title"},[e._v("账号密码管理")]),e._v(" "),a("div",{staticClass:"link-top"}),e._v(" "),a("div",{staticClass:"cm-search-div"},[e._v("\n    用户名："),a("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入用户名"},model:{value:e.userName,callback:function(t){e.userName=t},expression:"userName"}}),e._v(" "),a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-search"},on:{click:function(t){e.pager.currentPage=1,e.search()}}},[e._v("查询")]),e._v(" "),a("br"),e._v("\n    安全码："),a("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入安全码"},model:{value:e.SafeNumber,callback:function(t){e.SafeNumber=t},expression:"SafeNumber"}}),e._v(" "),a("span",{staticStyle:{color:"#F56C6C"}},[e._v("新密码：")]),a("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入新的密码"},model:{value:e.userpwd,callback:function(t){e.userpwd=t},expression:"userpwd"}}),e._v(" "),a("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-edit"},on:{click:function(t){e.pager.currentPage=1,e.sendCommand()}}},[e._v("修改密码")])],1),e._v(" "),a("div",{staticClass:"cm-title"},[e._v("用户信息")]),e._v(" "),a("div",{staticClass:"link-top"}),e._v(" "),a("br"),e._v(" "),a("div",[a("el-table",{attrs:{data:e.tableData,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"},border:"",stripe:""}},[a("el-table-column",{attrs:{prop:"user_id",label:"账号ID",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"username",label:"用户名"}}),e._v(" "),a("el-table-column",{attrs:{prop:"userpwd",label:"密码"}}),e._v(" "),a("el-table-column",{attrs:{prop:"activity",label:"状态",formatter:e.coverFlag}}),e._v(" "),a("el-table-column",{attrs:{prop:"safety",label:"安全码"}}),e._v(" "),a("el-table-column",{attrs:{prop:"phonenumber",label:"手机号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"loginip",label:"登录IP"}})],1)],1),e._v(" "),a("pagination",{staticClass:"cm-page",attrs:{pager:e.pager},on:{load:function(e){}}})],1)},staticRenderFns:[]};var c=a("VU/8")(l,n,!1,function(e){a("TIcu")},"data-v-0aeb8668",null);t.default=c.exports},TIcu:function(e,t){}});
//# sourceMappingURL=27.9cd0ebedb8e4376580c4.js.map