webpackJsonp([9],{Zfhn:function(t,e){},lySn:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a={name:"Tab",data:function(){return{activeTab:"first",msgtip:!1,logining:!1,fileList:[],tableData:[]}},filters:{formatDate:function(t){return new Date(parseInt(t)).toLocaleString().replace(/:\d{1,2}$/," ")}},created:function(){this.getPageData()},methods:{getPageData:function(){var t=this;this.$http.get("/api/readconfig").then(function(e){if(200==e.data.code)for(var s in t.tableData.splice(0,t.tableData.length),e.data.data)t.tableData.push({filename:s,modifytime:e.data.data[s],canDel:s.length-s.indexOf("xls")==16});else t.$alert("数据请求失败","提示",{confirmButtonText:"ok"})}).catch(function(t){console.log(t)})},submitBtn:function(){this.$refs.upload.submit()},uploadXls:function(t){var e=this;this.logining=!0;var s=new FormData;s.append("file",t.file),this.$http({url:"/api/uploadconfig",data:s,method:"post",headers:{"Content-Type":"multipart/form-data"}}).then(function(t){200==t.data.code?(e.$message.success("上传成功，配置刷新完毕"),e.fileList.splice(0,e.fileList.length),e.getPageData()):e.$alert(t.data.message,"提示",{confirmButtonText:"ok"}),e.logining=!1}).catch(function(t){e.logining=!1,console.log(t)})},down:function(t,e){var s=this;this.$http({url:"/api/downconfig?fileName="+e.filename,method:"get",responseType:"blob"}).then(function(t){if(null!=t.data.message&&""!=t.data.message)s.$alert(t.data.message,"提示",{confirmButtonText:"ok"});else{var a=new Blob([t.data],{type:"application/vnd.ms-excel"});if(window.navigator.msSaveBlob)window.navigator.msSaveBlob(a,e.filename);else{var l=document.createElement("a"),i=window.URL.createObjectURL(a);l.href=i,l.download=e.filename,document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(i)}}}).catch(function(t){console.log(t)})},del:function(t,e){var s=this;this.$confirm("你选择了文件【"+e.filename+"】此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){s.$http.delete("/api/delconfig?fileName="+e.filename).then(function(t){200==t.data.code?(s.$message.success("删除成功!"),s.getPageData()):s.$alert(t.data.message,"提示",{confirmButtonText:"ok"})}).catch(function(t){console.log(t)})}).catch(function(){s.$message({type:"info",message:"已取消删除"})})},handleRemove:function(t,e){console.log(t,e)},handlePreview:function(t){console.log(t)},handleRowClick:function(t,e,s){this.setCurRowChecked(t)},handleCheckedAllAndCheckedNone:function(t){1!=t.length&&this.$refs.list.setCurrentRow()},setCurRowChecked:function(t){this.$refs.list.clearSelection(),this.$refs.list.toggleRowSelection(t)}}},l={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("el-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[s("el-tab-pane",{attrs:{label:"配置文件列表",name:"first"}},[s("el-table",{ref:"list",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"","highlight-current-row":""},on:{"row-click":t.handleRowClick,"select-all":t.handleCheckedAllAndCheckedNone,select:t.handleCheckedAllAndCheckedNone}},[s("el-table-column",{attrs:{property:"filename",label:"文件名",width:"250",align:"center"}}),t._v(" "),s("el-table-column",{attrs:{property:"modifytime",label:"最后更新时间",width:"330",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(t._f("formatDate")(e.row.modifytime)))])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-button",{attrs:{type:"primary"},on:{click:function(s){return t.down(e.$index,e.row)}}},[t._v("下载文件"),s("i",{staticClass:"el-icon-download el-icon--right"})]),t._v(" "),e.row.canDel?s("el-button",{attrs:{type:"danger"},on:{click:function(s){return t.del(e.$index,e.row)}}},[t._v("删除文件"),s("i",{staticClass:"el-icon-close el-icon--right"})]):t._e()]}}])})],1)],1),t._v(" "),s("el-tab-pane",{attrs:{label:"配置文件更新",name:"second"}},[s("el-card",{attrs:{shadow:"always"}},[s("el-divider",[t._v(" 目前支持的配置类型 ")]),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"变身卡",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("acard.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"竞技称号",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("achieve.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"自动任务",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("active.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"炼化配置",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("alchemy.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"孩子结局",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("babyresult.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"石破烂、黑心商人回收",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("bbuy.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"日常活动",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("boos.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"孩子天资",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("child.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"颜色配置",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("color.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"配饰属性",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("decorate.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"大闹天宫科技",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("dntg.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"传送门",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("door.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"抽奖",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("draw.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"掉落",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("drop.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"仙玉商城",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("eshop.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"日常总览",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("event.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"升级所需经验表",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("exp.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"宝石",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("gem.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"神兵附加神兵石属性",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("godstone.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"引导界面",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("guide.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"物品",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("item.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"灵宝",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("lingbao.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"灵宝符石",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("lingbaofushi.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"随机商人",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("lShop.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"地图",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("map.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"怪物数据",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("monster.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"坐骑",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("mount.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"11-16装备",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("newequip.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"NPC功能",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("npc.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"伙伴数据",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("palData.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"伙伴装备",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("palEquip.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"召唤兽",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("pet.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"神兽兑换",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("petExchange.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"注册礼包",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("present.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"活动挂载机器人",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("robots.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"刷怪坐标",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("sghostpoint.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"杂货商、积分商人",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("shop.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"技能",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("skill.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"星卡属性",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("starPalace.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"套装",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("suit.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"任务数据",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("taskData.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"任务次数",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("taskSet.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"称谓属性",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("title.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"特效",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("tx.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"天演册",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("tyc.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"翅膀升级点数",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("wingTraining.xls")])],1),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"仙器属性",placement:"top"}},[s("el-button",{attrs:{size:"mini",type:"success",plain:""}},[t._v("xianqi.xls")])],1)],1),t._v(" "),s("el-alert",{attrs:{"show-icon":"",title:"只能上传上表中的xls文件，每次只能选择上传一个配置文件，上传成功后立即刷新配置（注意：文件名必须保持一致，包括大小写）",type:"success",effect:"dark",closable:!1}}),t._v(" "),s("el-card",{staticClass:"center",attrs:{shadow:"always"}},[s("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{action:"1111","on-preview":t.handlePreview,"on-remove":t.handleRemove,"file-list":t.fileList,multiple:!1,accept:".xls","http-request":t.uploadXls,limit:1,"auto-upload":!1}},[s("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[t._v("选取文件")]),t._v(" "),s("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"danger",loading:t.logining},on:{click:t.submitBtn}},[t._v("上传到服务器")])],1)],1)],1)],1)],1)},staticRenderFns:[]};var i=s("VU/8")(a,l,!1,function(t){s("Zfhn")},"data-v-6897b67c",null);e.default=i.exports}});
//# sourceMappingURL=9.4b17ce7c713cdd17c87b.js.map