webpackJsonp([7],{HBwJ:function(e,t){},uc8g:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("spLH"),l=(a("eNfa"),a("VsUZ")),n={name:"comment",components:{pagination:s.a},data:function(){return{userName:"",codeCard:"",tableData:[],pager:{totalCount:0,currentPage:1,pageSize:20}}},methods:{addGold:function(){var e=this,t={type:"insert",userName:this.userName,codeCard:this.codeCard};Object(l.e)(t).then(function(t){400==t.status?e.$message.error(t.message):200==t.status?(e.$message.success("操作成功"),e.initData()):e.$message.warning("未知错误")})},initData:function(){var e=this;Object(l.m)({type:"search"}).then(function(t){400==t.status?e.$message.error(t.message):200==t.status?e.tableData=t.list:e.$message.warning("未知错误")})}},mounted:function(){this.initData()}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cm-title"},[e._v("用户充值")]),e._v(" "),a("div",{staticClass:"link-top"}),e._v(" "),a("div",{staticClass:"cm-search-div"},[e._v("\n    账号："),a("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入账号"},model:{value:e.userName,callback:function(t){e.userName=t},expression:"userName"}}),e._v("\n    充值金额："),a("el-input",{staticClass:"cm-search-input",attrs:{placeholder:"请输入充值金额(RMB)"},model:{value:e.codeCard,callback:function(t){e.codeCard=t},expression:"codeCard"}}),e._v(" "),a("span",{staticStyle:{color:"red"}},[e._v("充值比例 1:1000 ,1RMB=1积分")]),e._v(" "),a("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-user-solid"},on:{click:function(t){e.pager.currentPage=1,e.addGold()}}},[e._v("充值")])],1),e._v(" "),a("div",{staticClass:"cm-title"},[e._v("充值记录")]),e._v(" "),a("div",{staticClass:"link-top"}),e._v(" "),a("br"),e._v(" "),a("div",[a("el-table",{attrs:{data:e.tableData,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"},border:"",stripe:""}},[a("el-table-column",{attrs:{prop:"createTime",label:"序号",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"playeracc",label:"充值账号",width:"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"recharge",label:"充值金额(RMB)","show-overflow-tooltip":!0,width:"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"yuanbao",label:"充值仙玉","show-overflow-tooltip":!0,width:"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"paytime",label:"充值时间"}})],1)],1),e._v(" "),a("pagination",{staticClass:"cm-page",attrs:{pager:e.pager},on:{load:function(e){}}})],1)},staticRenderFns:[]};var i=a("VU/8")(n,r,!1,function(e){a("HBwJ")},"data-v-988e0d62",null);t.default=i.exports}});
//# sourceMappingURL=7.8c4b0955cc05a5af9464.js.map